﻿import { UserRoleEnum } from "./Enums";
import MultiSelectModel from "./MultiSelectModel";

export class UserModel {
  constructor(
    public id: string | null = null,
    public name: string,
    public organizationName: string,
    public email: string,
    public userType: string,
    public identityId: string | null = null,
    public countryRequestedForIds: Array<MultiSelectModel> = [],
    public accessRequestedCountryIds: Array<MultiSelectModel> = [],
    public inActivatedCountryIds: Array<string> = [],
    public accessGrantedCountryIds: Array<string> = [],
    public pendingRequestCountryIds: Array<string> = []
  ) {
    this.id = id;
    this.userType = userType;
    this.countryRequestedForIds = countryRequestedForIds;
    this.identityId = identityId;
    this.organizationName = organizationName;
    this.name = name;
    this.email = email;
    this.accessRequestedCountryIds = accessRequestedCountryIds;
    this.inActivatedCountryIds = inActivatedCountryIds;
    this.accessGrantedCountryIds = accessGrantedCountryIds;
    this.pendingRequestCountryIds = pendingRequestCountryIds;
  }

  static init = () => new UserModel("", "", "", "", "");
}

export class UserCountryAccessModel {
  constructor(
    public id: string,
    public userId: string,
    public countryId: string,
    public status: number,
    public isActive: boolean
  ) {
    this.id = id;
    this.userId = id;
    this.countryId = countryId;
    this.status = status;
    this.isActive = isActive
  }
}

/** Class of current user and it's information(encrypted) */
export class CurrentUserModel {
  constructor(
    public email: string,
    public identityId: string,
    public isNewUser: boolean,
    public name: string,
    public userId: string,
    public userType: number,
    public scheme: string,
    public trackingId: string
  ) {
    this.email = email;
    this.isNewUser = isNewUser;
    this.identityId = identityId;
    this.name = name;
    this.userId = userId;
    this.userType = userType;
    this.scheme = scheme;
    this.trackingId = trackingId;
  }

  static init = () =>
    new CurrentUserModel("", "", false, "", "", UserRoleEnum.Viewer, "", "");
}

export class UserCountry {
  constructor(
    public id: string,
    public name: string,
    public userType: number,
    public isDefault: boolean
  ) {
    this.id = id;
    this.name = name;
    this.userType = userType;
    this.isDefault = isDefault;
  }
  static init = () => new UserCountry("", "", 0, false);
}