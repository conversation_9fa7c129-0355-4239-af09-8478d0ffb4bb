import { Browser<PERSON>outer, Route, Switch } from "react-router-dom";
import { AnimatePresence } from "framer-motion";

import Header from "../../components/ui/Header";
import Landing from "../landing/Landing";
import { useTranslation } from "react-i18next";
import DeactivatedUser from "../common/DeactivatedUser";
import InvitationAccepted from "../../components/user/InvitationAccepted";
import InActivatedUser from "../common/InActivatedUser";
import DeActivatedWhoUser from "../common/DeActivatedWhoUser";
import UnRegisteredUser from "../common/UnRegisteredUser";

function PublicRoutes() {
  const { t } = useTranslation();
  document.title = t("app.LandingPageTitle");
  return (
    <BrowserRouter>
      <Header />
      <AnimatePresence>
        <main className="app-main">
          <Switch>
            <Route path="/deactivateduser" >
              <DeactivatedUser />
            </Route>
            <Route path="/deactivatedwhouser" >
              <DeActivatedWhoUser />
            </Route>
            <Route path="/user/invitation/accept/:ucaId">
              <InvitationAccepted />
            </Route>
            <Route path="/inActivatedUser" >
              <InActivatedUser />
            </Route>
            <Route path="/unregistereduser" >
              <UnRegisteredUser />
            </Route>
            <Route path="/">
              <Landing />
            </Route>
          </Switch>
        </main>
      </AnimatePresence>
    </BrowserRouter >
  );
}

export default PublicRoutes;
