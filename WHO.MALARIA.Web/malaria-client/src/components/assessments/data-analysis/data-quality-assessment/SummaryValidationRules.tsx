﻿import { Constants } from "../../../../models/Constants";
import { DataType } from "../../../../models/Enums";
import ValidationRuleModel, {
  IValidationRuleProvider,
} from "../../../../models/ValidationRuleModel";

/** Summary DQA validation rules */
const SummaryValidationRules: IValidationRuleProvider = {
  dataQualityResultReason: new ValidationRuleModel(DataType.String, true),
  // Core indicators - allow null values (NA selections) by setting required=false
  confirmMalariaCasesNotified: new ValidationRuleModel(DataType.Boolean, false),
  confirmMalariaCasesInvestigated: new ValidationRuleModel(
    DataType.Boolean,
    false
  ),
  confirmMalariaCasesClassified: new ValidationRuleModel(
    DataType.Boolean,
    false
  ),
  confirmMalariaCasesClassifiedAsLocal: new ValidationRuleModel(
    DataType.Boolean,
    false
  ),
  confirmMalariaCasesClassifiedAsIndigenous: new ValidationRuleModel(
    DataType.Boolean,
    false
  ),
  confirmMalariaCasesClassifiedAsIntroduced: new ValidationRuleModel(
    DataType.Boolean,
    false
  ),
  confirmMalariaCasesClassifiedAsImported: new ValidationRuleModel(
    DataType.Boolean,
    false
  ),
  malariaCasesDueToPF: new ValidationRuleModel(DataType.Boolean, false),
  malariaCasesDueToPK: new ValidationRuleModel(DataType.Boolean, false),
  malariaCasesDueToPM: new ValidationRuleModel(DataType.Boolean, false),
  malariaCasesDueToPO: new ValidationRuleModel(DataType.Boolean, false),
  malariaCasesDueToPV: new ValidationRuleModel(DataType.Boolean, false),
  reportCompleteness: new ValidationRuleModel(DataType.Boolean, true),
  keyVariableConcordanceBtwTwoReportingSystem: new ValidationRuleModel(
    DataType.Boolean,
    true
  ),
  consistencyOverTimeCoreIndicators: new ValidationRuleModel(
    DataType.Boolean,
    true
  ),

  reportTimeliness: new ValidationRuleModel(
    DataType.Number,
    true,
    `${Constants.Common.RootObjectNameSubstitute}.reportTimeliness !==null && !(${Constants.Common.RootObjectNameSubstitute}.reportTimeliness >=0 && ${Constants.Common.RootObjectNameSubstitute}.reportTimeliness <=100)`,
    "Errors.ValueBetweenZeroToHundred"
  ),
  caseInvestigationReportsCompleteness: new ValidationRuleModel(
    DataType.Number,
    true,
    `${Constants.Common.RootObjectNameSubstitute}.caseInvestigationReportsCompleteness !==null && !(${Constants.Common.RootObjectNameSubstitute}.caseInvestigationReportsCompleteness >=0 && ${Constants.Common.RootObjectNameSubstitute}.caseInvestigationReportsCompleteness <=100)`,
    "Errors.ValueBetweenZeroToHundred"
  ),
  caseNotificationReportsTimeliness: new ValidationRuleModel(
    DataType.Number,
    true,
    `${Constants.Common.RootObjectNameSubstitute}.caseNotificationReportsTimeliness !==null && !(${Constants.Common.RootObjectNameSubstitute}.caseNotificationReportsTimeliness >=0 && ${Constants.Common.RootObjectNameSubstitute}.caseNotificationReportsTimeliness <=100)`,
    "Errors.ValueBetweenZeroToHundred"
  ),
  caseInvestigationReportsTimeliness: new ValidationRuleModel(
    DataType.Number,
    true,
    `${Constants.Common.RootObjectNameSubstitute}.caseInvestigationReportsTimeliness !==null && !(${Constants.Common.RootObjectNameSubstitute}.caseInvestigationReportsTimeliness >=0 && ${Constants.Common.RootObjectNameSubstitute}.caseInvestigationReportsTimeliness <=100)`,
    "Errors.ValueBetweenZeroToHundred"
  ),
  fociInvestigationReportsTimeliness: new ValidationRuleModel(
    DataType.Number,
    true,
    `${Constants.Common.RootObjectNameSubstitute}.fociInvestigationReportsTimeliness !==null && !(${Constants.Common.RootObjectNameSubstitute}.fociInvestigationReportsTimeliness >=0 && ${Constants.Common.RootObjectNameSubstitute}.fociInvestigationReportsTimeliness <=100)`,
    "Errors.ValueBetweenZeroToHundred"
  ),
  coreVariableCompletenessWithinReport: new ValidationRuleModel(
    DataType.Number,
    true,
    `${Constants.Common.RootObjectNameSubstitute}.coreVariableCompletenessWithinReport !==null && !(${Constants.Common.RootObjectNameSubstitute}.coreVariableCompletenessWithinReport >=0 && ${Constants.Common.RootObjectNameSubstitute}.coreVariableCompletenessWithinReport <=100)`,
    "Errors.ValueBetweenZeroToHundred"
  ),
  consistencyBetweenCoreVariables: new ValidationRuleModel(
    DataType.Number,
    true,
    `${Constants.Common.RootObjectNameSubstitute}.consistencyBetweenCoreVariables !==null && !(${Constants.Common.RootObjectNameSubstitute}.consistencyBetweenCoreVariables >=0 && ${Constants.Common.RootObjectNameSubstitute}.consistencyBetweenCoreVariables <=100)`,
    "Errors.ValueBetweenZeroToHundred"
  ),
  coreVariableCompletenessWithinRegister: new ValidationRuleModel(
    DataType.Number,
    true,
    `${Constants.Common.RootObjectNameSubstitute}.coreVariableCompletenessWithinRegister !==null && !(${Constants.Common.RootObjectNameSubstitute}.coreVariableCompletenessWithinRegister >=0 && ${Constants.Common.RootObjectNameSubstitute}.coreVariableCompletenessWithinRegister <=100)`,
    "Errors.ValueBetweenZeroToHundred"
  ),
  coreVariableConcordanceBtwRegister: new ValidationRuleModel(
    DataType.Number,
    true,
    `${Constants.Common.RootObjectNameSubstitute}.coreVariableConcordanceBtwRegister !==null && !(${Constants.Common.RootObjectNameSubstitute}.coreVariableConcordanceBtwRegister >=0 && ${Constants.Common.RootObjectNameSubstitute}.coreVariableConcordanceBtwRegister <=100)`,
    "Errors.ValueBetweenZeroToHundred"
  ),
  year: new ValidationRuleModel(
    DataType.Number,
    true,
    `${Constants.Common.RootObjectNameSubstitute}.year !==null && (${Constants.Common.RootObjectNameSubstitute}.year <= 0)`
  ),
};

export default SummaryValidationRules;
