import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import classNames from "classnames";
import { IndicatorModel } from "../../../../models/DQA/IndicatorModel";
import { dqaService } from "../../../../services/dqaService";
import Table from "../../data-collection/desk-review/responses/Table";
import TableHeader from "../../data-collection/desk-review/responses/TableHeader";
import TableBody from "../../data-collection/desk-review/responses/TableBody";
import TableRow from "../../data-collection/desk-review/responses/TableRow";
import TableCell from "../../data-collection/desk-review/responses/TableCell";
import TextBox from "../../../controls/TextBox";
import RadioButtonGroup from "../../../controls/RadioButtonGroup";
import MultiSelectModel from "../../../../models/MultiSelectModel";
import {
  EliminationSummaryModel,
  SummaryModel,
} from "../../../../models/DQA/EliminationSummaryModel";
import {
  ConsistentTrendValue,
  ConsistentTrendHelper,
} from "../../../../models/DQA/DeskLevelSummaryReportModel";
import { Button } from "@mui/material";
import useYears from "../../data-collection/useYears";
import Dropdown from "../../../controls/Dropdown";
import { UserAssessmentPermission } from "../../../../models/PermissionModel";
import { useSelector } from "react-redux";
import SummaryValidationRules from "./SummaryValidationRules";
import useFormValidation from "../../../common/useFormValidation";
import DownloadInProgressModal from "../../../controls/DownloadInProgressModal";
import { UtilityHelper } from "../../../../utils/UtilityHelper";

const DATA_ITEM_KEY = "id";
const SELECTED_FIELD = "selected";

type SummaryResultProps = {
  strategyId: string;
  assessmentId: string;
};

/** Renders the summary result for elimination DQA */
const EliminationSummaryResult = (props: SummaryResultProps) => {
  const { t } = useTranslation();
  document.title = t("app.DQASummary");
  const { assessmentId } = props;
  const [summary, setSummary] = useState<EliminationSummaryModel>(
    EliminationSummaryModel.init(assessmentId)
  );
  const years = useYears();

  const userPermission: UserAssessmentPermission = useSelector(
    (state: any) => state.userPermission.assessment
  );
  const {
    canEditAndSaveDeskLevelDQASummaryResult,
    canFinalizeDeskLevelDQASummaryResult,
    canExportDeskLevelDQASummary,
  } = userPermission;
  const [isDataSaved, setIsDataSaved] = useState<boolean>(false);
  const [isFileDownloading, setIsFileDownloading] = useState<boolean>(false);
  const [selectedIndicators, setSelectedIndicators] = useState<Array<string>>(
    []
  );

  const validate = useFormValidation(SummaryValidationRules);
  const errors = useSelector((state: any) => state.error);

  useEffect(() => {
    getEliminationDQASummary(assessmentId);
  }, []);

  // Get elimination dqa summary response by assessmentId
  const getEliminationDQASummary = (assessmentId: string) => {
    dqaService.getEliminationDQASummary(assessmentId).then(response => {
      setSummary(response);
      if (response.confirmMalariaCasesInvestigated != null) {
        setIsDataSaved(true);
      }
    });
  };

  // Triggers whenever user clicks on Generate Template button
  const onGenerateTemplate = () => {
    setIsFileDownloading(true);
    const downloadedTemplateName = t("app.DQAEliminationToolReportFileName");
    dqaService
      .generateEliminationDQATemplate(assessmentId)
      .then((response: any) => {
        UtilityHelper.download(response, downloadedTemplateName);
        setIsFileDownloading(false);
      })
      .catch((error: any) => {
        setIsFileDownloading(false);
      });
  };

  // Triggers whenever user clicks on Save button
  const onSave = () => {
    const isFormValid = validate(summary);

    if (isFormValid) {
      const request: SummaryModel = new SummaryModel(
        new EliminationSummaryModel(
          assessmentId,
          summary.reportCompleteness,
          summary.reportTimeliness,
          summary.caseInvestigationReportsCompleteness,
          summary.caseNotificationReportsTimeliness,
          summary.caseInvestigationReportsTimeliness,
          summary.fociInvestigationReportsTimeliness,
          summary.coreVariableCompletenessWithinReport,
          summary.consistencyBetweenCoreVariables,
          summary.consistencyOverTimeCoreIndicators,
          summary.confirmMalariaCasesNotified,
          summary.confirmMalariaCasesInvestigated,
          summary.confirmMalariaCasesClassified,
          summary.confirmMalariaCasesClassifiedAsLocal,
          summary.confirmMalariaCasesClassifiedAsIndigenous,
          summary.confirmMalariaCasesClassifiedAsIntroduced,
          summary.confirmMalariaCasesClassifiedAsImported,
          summary.malariaCasesDueToPF,
          summary.malariaCasesDueToPK,
          summary.malariaCasesDueToPM,
          summary.malariaCasesDueToPO,
          summary.malariaCasesDueToPV,
          summary.keyVariableConcordanceBtwTwoReportingSystem,
          summary.coreVariableCompletenessWithinRegister,
          summary.coreVariableConcordanceBtwRegister,
          summary.dataQualityResultReason,
          summary.year,
          false
        )
      );

      dqaService
        .saveEliminationDQASummary(request)
        .then((response: boolean) => {
          if (response) {
            setIsDataSaved(true);
          }
        });
    }
  };

  // Triggers whenever user clicks on finalize button
  const onFinalize = () => {
    const isFormValid = validate(summary);

    if (isFormValid) {
      const request: SummaryModel = new SummaryModel(
        new EliminationSummaryModel(
          assessmentId,
          summary.reportCompleteness,
          summary.reportTimeliness,
          summary.caseInvestigationReportsCompleteness,
          summary.caseNotificationReportsTimeliness,
          summary.caseInvestigationReportsTimeliness,
          summary.fociInvestigationReportsTimeliness,
          summary.coreVariableCompletenessWithinReport,
          summary.consistencyBetweenCoreVariables,
          summary.consistencyOverTimeCoreIndicators,
          summary.confirmMalariaCasesNotified,
          summary.confirmMalariaCasesInvestigated,
          summary.confirmMalariaCasesClassified,
          summary.confirmMalariaCasesClassifiedAsLocal,
          summary.confirmMalariaCasesClassifiedAsIndigenous,
          summary.confirmMalariaCasesClassifiedAsIntroduced,
          summary.confirmMalariaCasesClassifiedAsImported,
          summary.malariaCasesDueToPF,
          summary.malariaCasesDueToPK,
          summary.malariaCasesDueToPM,
          summary.malariaCasesDueToPO,
          summary.malariaCasesDueToPV,
          summary.keyVariableConcordanceBtwTwoReportingSystem,
          summary.coreVariableCompletenessWithinRegister,
          summary.coreVariableConcordanceBtwRegister,
          summary.dataQualityResultReason,
          summary.year,
          true
        )
      );

      dqaService.finalizeEliminationDQASummary(request).then(response => {
        if (response) {
          setSummary({
            ...summary,
            isFinalized: response as boolean,
          });
        }
      });
    }
  };

  const headersRows = [
    {
      field: "summaryNationalDataQualityEstimates",
      label: t(
        "Assessment.DataCollection.DataQualityAssessment.SummaryNationalDataQualityEstimates"
      ),
    },
    {
      field: "nationalLevelResults",
      label: t(
        "Assessment.DataCollection.DataQualityAssessment.NationalLevelResults"
      ),
    },
  ];

  // Triggers whenever the user modifies the radiobox change event
  const onRadioValueChange = (
    fieldName: string,
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const _summaryData = {
      ...summary,
      [fieldName]: e.target.value === "true" ? true : false,
    };
    _summaryData.consistencyOverTimeCoreIndicators =
      selectedIndicators.length === 11 ? true : false;
    setSummary(_summaryData);
  };

  //update 1.2.9 values automaically Met/NotMet
  const bindConsistencyOverTimeForCoreIndicators = (
    value: boolean | null,
    fieldName: string
  ) => {
    if (value === true) {
      if (!selectedIndicators.includes(fieldName)) {
        const currentSelectedIndicators = [...selectedIndicators];
        currentSelectedIndicators.push(fieldName);
        setSelectedIndicators(currentSelectedIndicators);
      }
      return "true";
    } else {
      const index = selectedIndicators.indexOf(fieldName);
      if (index !== -1) {
        const currentSelectedIndicators = [...selectedIndicators];
        currentSelectedIndicators.splice(index, 1);
        setSelectedIndicators(currentSelectedIndicators);
        return "false";
      }
    }
  };

  // Triggers whenever the user modifies the textbox change event
  //if value is removed then null value is assigned
  const onValueChange = (
    fieldName: string,
    evt: React.ChangeEvent<HTMLInputElement>
  ) => {
    const _summaryData = {
      ...summary,
      [fieldName]: evt.target.value === "" ? null : +evt.currentTarget.value,
    };

    setSummary(_summaryData);
  };

  // Triggers whenever the user modifies the result reason change event
  const dataQualityResultReason = (
    fieldName: string,
    evt: React.ChangeEvent<HTMLInputElement>
  ) => {
    const _summaryData = {
      ...summary,
      [fieldName]: evt.currentTarget.value,
    };

    setSummary(_summaryData);
  };

  // Get CSS class for completeness core variables to highlight percentage value
  const getCssClassForNationalLevelResult = (value: any) => {
    if (!value && value !== 0) {
      return;
    }
    switch (true) {
      case value < 80 || value > 100:
        return "bg-red";
      case value >= 80 && value <= 95:
        return "bg-yellow";
      case value > 95 && value <= 100:
        return "bg-green";
      default:
        return "";
    }
  };

  return (
    <>
      <div className={classNames("summary-section", "p-3")}>
        <div className='row justify-content-center'>
          <div id='elimination-dqa-summary-section'>
            <span>
              {t(
                "Assessment.DataCollection.DataQualityAssessment.DQAEliminationQualityEstimateMessage"
              )}
            </span>
          </div>

          <div className='col-sm-3' id='elimination-dqa-year-margin'>
            <Dropdown
              id='year'
              name='year'
              variant='outlined'
              size='small'
              label={t("Common.YearOfData")}
              value={summary?.year}
              options={years.map(year => {
                return new MultiSelectModel(
                  year,
                  year.toString(),
                  false,
                  false
                );
              })}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                onValueChange("year", e)
              }
              error={errors["year"] && errors["year"]}
              helperText={errors["year"] && errors["year"]}
            />
          </div>
        </div>

        <Table>
          <>
            <TableHeader
              headers={headersRows.map((header: any) => header.label)}
            />
            <TableBody>
              <>
                <TableRow>
                  <>
                    <TableCell>
                      <span>
                        1.2.1 &nbsp;{" "}
                        {t(
                          "Assessment.DataCollection.DataQualityAssessment.ReportCompleteness"
                        )}
                      </span>
                    </TableCell>
                    <TableCell>
                      <RadioButtonGroup
                        id='reportCompleteness'
                        name='reportCompleteness'
                        color='primary'
                        options={[
                          new MultiSelectModel(
                            true,
                            t("indicators-responses:Common:Met")
                          ),
                          new MultiSelectModel(
                            false,
                            t("indicators-responses:Common:NotMet")
                          ),
                        ]}
                        value={summary.reportCompleteness}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                          onRadioValueChange("reportCompleteness", e)
                        }
                      />
                    </TableCell>
                  </>
                </TableRow>
                <TableRow>
                  <>
                    <TableCell>
                      <span>
                        1.2.2 &nbsp;{" "}
                        {t(
                          "Assessment.DataCollection.DataQualityAssessment.CaseInvestigationReportsCompleteness"
                        )}
                      </span>
                    </TableCell>
                    <TableCell>
                      <TextBox
                        id='caseInvestigationReportsCompleteness'
                        type='number'
                        name='caseInvestigationReportsCompleteness'
                        maxLength={3}
                        fullWidth
                        inputProps={{
                          max: 100,
                          min: 0,
                        }}
                        className={`col-form-control inputfocus ${getCssClassForNationalLevelResult(summary?.caseInvestigationReportsCompleteness)}`}
                        value={summary.caseInvestigationReportsCompleteness}
                        onChange={(
                          evt: React.ChangeEvent<HTMLInputElement>
                        ) => {
                          onValueChange(
                            "caseInvestigationReportsCompleteness",
                            evt
                          );
                        }}
                        error={
                          errors["caseInvestigationReportsCompleteness"] &&
                          errors["caseInvestigationReportsCompleteness"]
                        }
                        helperText={
                          errors["caseInvestigationReportsCompleteness"] &&
                          errors["caseInvestigationReportsCompleteness"]
                        }
                      />
                    </TableCell>
                  </>
                </TableRow>
                {/* <TableRow>
                                    <>
                                        <TableCell>
                                            <span>1.2.3 &nbsp; {t("Assessment.DataCollection.DataQualityAssessment.ReportTimeliness")}
                                            </span>
                                        </TableCell>
                                        <TableCell>
                                            <TextBox
                                                id="reportTimeliness"
                                                type="number"
                                                name="reportTimeliness"
                                                maxLength={3}
                                                fullWidth
                                                inputProps={{
                                                    max: 100,
                                                    min: 0,
                                                }}
                                                className={`col-form-control inputfocus ${getCssClassForNationalLevelResult(summary?.reportTimeliness)}`}
                                                value={summary.reportTimeliness}
                                                onChange={(evt: React.ChangeEvent<HTMLInputElement>) => {
                                                    onValueChange("reportTimeliness", evt);
                                                }}
                                                error={
                                                    errors["reportTimeliness"] &&
                                                    errors["reportTimeliness"]
                                                }
                                                helperText={
                                                    errors["reportTimeliness"] &&
                                                    errors["reportTimeliness"]
                                                }
                                            />
                                        </TableCell>
                                    </>
                                </TableRow> */}
                <TableRow>
                  <>
                    <TableCell>
                      <span>
                        1.2.4 &nbsp;
                        {t(
                          "Assessment.DataCollection.DataQualityAssessment.CaseNotificationReportsTimeliness"
                        )}
                      </span>
                    </TableCell>
                    <TableCell>
                      <TextBox
                        id='caseNotificationReportsTimeliness'
                        type='number'
                        name='caseNotificationReportsTimeliness'
                        maxLength={3}
                        fullWidth
                        inputProps={{
                          max: 100,
                          min: 0,
                        }}
                        className={`col-form-control inputfocus ${getCssClassForNationalLevelResult(summary?.caseNotificationReportsTimeliness)}`}
                        value={summary.caseNotificationReportsTimeliness}
                        onChange={(
                          evt: React.ChangeEvent<HTMLInputElement>
                        ) => {
                          onValueChange(
                            "caseNotificationReportsTimeliness",
                            evt
                          );
                        }}
                        error={
                          errors["caseNotificationReportsTimeliness"] &&
                          errors["caseNotificationReportsTimeliness"]
                        }
                        helperText={
                          errors["caseNotificationReportsTimeliness"] &&
                          errors["caseNotificationReportsTimeliness"]
                        }
                      />
                    </TableCell>
                  </>
                </TableRow>
                <TableRow>
                  <>
                    <TableCell>
                      <span>
                        1.2.5 &nbsp;
                        {t(
                          "Assessment.DataCollection.DataQualityAssessment.CaseInvestigationReportsTimeliness"
                        )}
                      </span>
                    </TableCell>
                    <TableCell>
                      <TextBox
                        id='caseInvestigationReportsTimeliness'
                        type='number'
                        name='caseInvestigationReportsTimeliness'
                        maxLength={3}
                        fullWidth
                        inputProps={{
                          max: 100,
                          min: 0,
                        }}
                        className={`col-form-control inputfocus ${getCssClassForNationalLevelResult(summary?.caseInvestigationReportsTimeliness)}`}
                        value={summary.caseInvestigationReportsTimeliness}
                        onChange={(
                          evt: React.ChangeEvent<HTMLInputElement>
                        ) => {
                          onValueChange(
                            "caseInvestigationReportsTimeliness",
                            evt
                          );
                        }}
                        error={
                          errors["caseInvestigationReportsTimeliness"] &&
                          errors["caseInvestigationReportsTimeliness"]
                        }
                        helperText={
                          errors["caseInvestigationReportsTimeliness"] &&
                          errors["caseInvestigationReportsTimeliness"]
                        }
                      />
                    </TableCell>
                  </>
                </TableRow>
                <TableRow>
                  <>
                    <TableCell>
                      <span>
                        1.2.6 &nbsp;
                        {t(
                          "Assessment.DataCollection.DataQualityAssessment.FociInvestigationReportsTimeliness"
                        )}
                      </span>
                    </TableCell>
                    <TableCell>
                      <TextBox
                        id='fociInvestigationReportsTimeliness'
                        type='number'
                        name='fociInvestigationReportsTimeliness'
                        maxLength={3}
                        fullWidth
                        inputProps={{
                          max: 100,
                          min: 0,
                        }}
                        className={`col-form-control inputfocus ${getCssClassForNationalLevelResult(summary?.fociInvestigationReportsTimeliness)}`}
                        value={summary.fociInvestigationReportsTimeliness}
                        onChange={(
                          evt: React.ChangeEvent<HTMLInputElement>
                        ) => {
                          onValueChange(
                            "fociInvestigationReportsTimeliness",
                            evt
                          );
                        }}
                        error={
                          errors["fociInvestigationReportsTimeliness"] &&
                          errors["fociInvestigationReportsTimeliness"]
                        }
                        helperText={
                          errors["fociInvestigationReportsTimeliness"] &&
                          errors["fociInvestigationReportsTimeliness"]
                        }
                      />
                    </TableCell>
                  </>
                </TableRow>
                <TableRow>
                  <>
                    <TableCell>
                      <span>
                        1.2.7 &nbsp;
                        {t(
                          "Assessment.DataCollection.DataQualityAssessment.CoreVariableCompletenessWithinReport"
                        )}
                      </span>
                    </TableCell>
                    <TableCell>
                      <TextBox
                        id='coreVariableCompletenessWithinReport'
                        type='number'
                        name='coreVariableCompletenessWithinReport'
                        maxLength={3}
                        fullWidth
                        inputProps={{
                          max: 100,
                          min: 0,
                        }}
                        className={`col-form-control inputfocus ${getCssClassForNationalLevelResult(summary?.coreVariableCompletenessWithinReport)}`}
                        value={summary.coreVariableCompletenessWithinReport}
                        onChange={(
                          evt: React.ChangeEvent<HTMLInputElement>
                        ) => {
                          onValueChange(
                            "coreVariableCompletenessWithinReport",
                            evt
                          );
                        }}
                        error={
                          errors["coreVariableCompletenessWithinReport"] &&
                          errors["coreVariableCompletenessWithinReport"]
                        }
                        helperText={
                          errors["coreVariableCompletenessWithinReport"] &&
                          errors["coreVariableCompletenessWithinReport"]
                        }
                      />
                    </TableCell>
                  </>
                </TableRow>
                <TableRow>
                  <>
                    <TableCell>
                      <span>
                        1.2.8 &nbsp;
                        {t(
                          "Assessment.DataCollection.DataQualityAssessment.ConsistencyBetweenCoreVariables"
                        )}
                      </span>
                    </TableCell>
                    <TableCell>
                      <TextBox
                        id='consistencyBetweenCoreVariables'
                        type='number'
                        name='consistencyBetweenCoreVariables'
                        maxLength={3}
                        fullWidth
                        inputProps={{
                          max: 100,
                          min: 0,
                        }}
                        className={`col-form-control inputfocus ${getCssClassForNationalLevelResult(summary?.consistencyBetweenCoreVariables)}`}
                        value={summary.consistencyBetweenCoreVariables}
                        onChange={(
                          evt: React.ChangeEvent<HTMLInputElement>
                        ) => {
                          onValueChange("consistencyBetweenCoreVariables", evt);
                        }}
                        error={
                          errors["consistencyBetweenCoreVariables"] &&
                          errors["consistencyBetweenCoreVariables"]
                        }
                        helperText={
                          errors["consistencyBetweenCoreVariables"] &&
                          errors["consistencyBetweenCoreVariables"]
                        }
                      />
                    </TableCell>
                  </>
                </TableRow>
                <TableRow>
                  <>
                    <TableCell>
                      <span>
                        1.2.9 &nbsp;
                        {t(
                          "Assessment.DataCollection.DataQualityAssessment.ConsistencyOvertimeCoreIndicators"
                        )}
                      </span>
                      <p>
                        <i>
                          {t(
                            "Assessment.DataCollection.DataQualityAssessment.Footnote1.2.9"
                          )}
                        </i>
                      </p>
                      {
                        //Show error message if Consistency Overtime Core Indicators aren't selected
                        (errors["confirmMalariaCasesNotified"] ||
                          errors["confirmMalariaCasesInvestigated"] ||
                          errors["confirmMalariaCasesClassified"] ||
                          errors["confirmMalariaCasesClassifiedAsLocal"] ||
                          errors["confirmMalariaCasesClassifiedAsIndigenous"] ||
                          errors["confirmMalariaCasesClassifiedAsIntroduced"] ||
                          errors["confirmMalariaCasesClassifiedAsImported"] ||
                          errors["malariaCasesDueToPF"] ||
                          errors["malariaCasesDueToPK"] ||
                          errors["malariaCasesDueToPM"] ||
                          errors["malariaCasesDueToPO"] ||
                          errors["malariaCasesDueToPV"]) && (
                          <span className='Mui-error d-flex mb-2'>
                            *
                            {t(
                              "Assessment.DataCollection.DataQualityAssessment.CoreIndicatorsError"
                            )}
                          </span>
                        )
                      }
                    </TableCell>
                    <TableCell>
                      <div className='disableContent'>
                        <RadioButtonGroup
                          id='consistencyOverTimeCoreIndicators'
                          name='consistencyOverTimeCoreIndicators'
                          color='primary'
                          options={[
                            new MultiSelectModel(
                              true,
                              t("indicators-responses:Common:Met")
                            ),
                            new MultiSelectModel(
                              false,
                              t("indicators-responses:Common:NotMet")
                            ),
                          ]}
                          value={selectedIndicators.length === 12}
                        />
                      </div>
                    </TableCell>
                  </>
                </TableRow>
                <TableRow className='border'>
                  <>
                    <TableCell>
                      <b>
                        {t(
                          "Assessment.DataCollection.DataQualityAssessment.CoreIndicators"
                        )}
                      </b>
                    </TableCell>
                    <TableCell>
                      <b>
                        {t(
                          "Assessment.DataCollection.DataQualityAssessment.ConsistentTrend"
                        )}
                      </b>
                    </TableCell>
                  </>
                </TableRow>
                <TableRow>
                  <>
                    <TableCell>
                      <span className='px-3'>
                        1.&nbsp;
                        {t(
                          "Assessment.DataCollection.DataQualityAssessment.ConfirmMalariaCasesNotified"
                        )}
                      </span>
                    </TableCell>
                    <TableCell>
                      <RadioButtonGroup
                        id='confirmMalariaCasesNotified'
                        name='confirmMalariaCasesNotified'
                        color='primary'
                        options={[
                          new MultiSelectModel(
                            ConsistentTrendValue.Yes,
                            t("indicators-responses:Common:Yes")
                          ),
                          new MultiSelectModel(
                            ConsistentTrendValue.No,
                            t("indicators-responses:Common:No")
                          ),
                          new MultiSelectModel(
                            ConsistentTrendValue.NA,
                            t("indicators-responses:Common:NA")
                          ),
                        ]}
                        value={ConsistentTrendHelper.fromBoolean(
                          summary.confirmMalariaCasesNotified
                        )}
                        defaultValue={bindConsistencyOverTimeForCoreIndicators(
                          summary?.confirmMalariaCasesNotified,
                          "confirmMalariaCasesNotified"
                        )}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                          onRadioValueChange("confirmMalariaCasesNotified", e)
                        }
                      />
                    </TableCell>
                  </>
                </TableRow>
                <TableRow>
                  <>
                    <TableCell>
                      <span className='px-3'>
                        2.&nbsp;
                        {t(
                          "Assessment.DataCollection.DataQualityAssessment.ConfirmMalariaCasesInvestigated"
                        )}
                      </span>
                    </TableCell>
                    <TableCell>
                      <RadioButtonGroup
                        id='confirmMalariaCasesInvestigated'
                        name='confirmMalariaCasesInvestigated'
                        color='primary'
                        options={[
                          new MultiSelectModel(
                            ConsistentTrendValue.Yes,
                            t("indicators-responses:Common:Yes")
                          ),
                          new MultiSelectModel(
                            ConsistentTrendValue.No,
                            t("indicators-responses:Common:No")
                          ),
                          new MultiSelectModel(
                            ConsistentTrendValue.NA,
                            t("indicators-responses:Common:NA")
                          ),
                        ]}
                        value={ConsistentTrendHelper.fromBoolean(summary.confirmMalariaCasesInvestigated)}
                        defaultValue={bindConsistencyOverTimeForCoreIndicators(
                          summary?.confirmMalariaCasesInvestigated,
                          "confirmMalariaCasesInvestigated"
                        )}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                          onRadioValueChange(
                            "confirmMalariaCasesInvestigated",
                            e
                          )
                        }
                      />
                    </TableCell>
                  </>
                </TableRow>
                <TableRow>
                  <>
                    <TableCell>
                      <span className='px-3'>
                        3.&nbsp;
                        {t(
                          "Assessment.DataCollection.DataQualityAssessment.ConfirmMalariaCasesClassified"
                        )}
                      </span>
                    </TableCell>
                    <TableCell>
                      <RadioButtonGroup
                        id='confirmMalariaCasesClassified'
                        name='confirmMalariaCasesClassified'
                        color='primary'
                        options={[
                          new MultiSelectModel(
                            true,
                            t("indicators-responses:Common:Yes")
                          ),
                          new MultiSelectModel(
                            false,
                            t("indicators-responses:Common:No")
                          ),
                        ]}
                        value={summary.confirmMalariaCasesClassified}
                        defaultValue={bindConsistencyOverTimeForCoreIndicators(
                          summary?.confirmMalariaCasesClassified,
                          "confirmMalariaCasesClassified"
                        )}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                          onRadioValueChange("confirmMalariaCasesClassified", e)
                        }
                      />
                    </TableCell>
                  </>
                </TableRow>
                <TableRow>
                  <>
                    <TableCell>
                      <span className='px-3'>
                        4.&nbsp;
                        {t(
                          "Assessment.DataCollection.DataQualityAssessment.ConfirmMalariaCasesClassifiedAsLocal"
                        )}
                      </span>
                    </TableCell>
                    <TableCell>
                      <RadioButtonGroup
                        id='confirmMalariaCasesClassifiedAsLocal'
                        name='confirmMalariaCasesClassifiedAsLocal'
                        color='primary'
                        options={[
                          new MultiSelectModel(
                            true,
                            t("indicators-responses:Common:Yes")
                          ),
                          new MultiSelectModel(
                            false,
                            t("indicators-responses:Common:No")
                          ),
                        ]}
                        value={summary.confirmMalariaCasesClassifiedAsLocal}
                        defaultValue={bindConsistencyOverTimeForCoreIndicators(
                          summary?.confirmMalariaCasesClassifiedAsLocal,
                          "confirmMalariaCasesClassifiedAsLocal"
                        )}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                          onRadioValueChange(
                            "confirmMalariaCasesClassifiedAsLocal",
                            e
                          )
                        }
                      />
                    </TableCell>
                  </>
                </TableRow>
                <TableRow>
                  <>
                    <TableCell>
                      <span className='px-3'>
                        5.&nbsp;
                        {t(
                          "Assessment.DataCollection.DataQualityAssessment.ConfirmMalariaCasesClassifiedAsIndigenous"
                        )}
                      </span>
                    </TableCell>
                    <TableCell>
                      <RadioButtonGroup
                        id='confirmMalariaCasesClassifiedAsIndigenous'
                        name='confirmMalariaCasesClassifiedAsIndigenous'
                        color='primary'
                        options={[
                          new MultiSelectModel(
                            true,
                            t("indicators-responses:Common:Yes")
                          ),
                          new MultiSelectModel(
                            false,
                            t("indicators-responses:Common:No")
                          ),
                        ]}
                        value={
                          summary.confirmMalariaCasesClassifiedAsIndigenous
                        }
                        defaultValue={bindConsistencyOverTimeForCoreIndicators(
                          summary?.confirmMalariaCasesClassifiedAsIndigenous,
                          "confirmMalariaCasesClassifiedAsIndigenous"
                        )}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                          onRadioValueChange(
                            "confirmMalariaCasesClassifiedAsIndigenous",
                            e
                          )
                        }
                      />
                    </TableCell>
                  </>
                </TableRow>
                <TableRow>
                  <>
                    <TableCell>
                      <span className='px-3'>
                        6.&nbsp;
                        {t(
                          "Assessment.DataCollection.DataQualityAssessment.ConfirmMalariaCasesClassifiedAsIntroduced"
                        )}
                      </span>
                    </TableCell>
                    <TableCell>
                      <RadioButtonGroup
                        id='confirmMalariaCasesClassifiedAsIntroduced'
                        name='confirmMalariaCasesClassifiedAsIntroduced'
                        color='primary'
                        options={[
                          new MultiSelectModel(
                            true,
                            t("indicators-responses:Common:Yes")
                          ),
                          new MultiSelectModel(
                            false,
                            t("indicators-responses:Common:No")
                          ),
                        ]}
                        value={
                          summary.confirmMalariaCasesClassifiedAsIntroduced
                        }
                        defaultValue={bindConsistencyOverTimeForCoreIndicators(
                          summary?.confirmMalariaCasesClassifiedAsIntroduced,
                          "confirmMalariaCasesClassifiedAsIntroduced"
                        )}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                          onRadioValueChange(
                            "confirmMalariaCasesClassifiedAsIntroduced",
                            e
                          )
                        }
                      />
                    </TableCell>
                  </>
                </TableRow>
                <TableRow>
                  <>
                    <TableCell>
                      <span className='px-3'>
                        7.&nbsp;
                        {t(
                          "Assessment.DataCollection.DataQualityAssessment.ConfirmMalariaCasesClassifiedAsImported"
                        )}
                      </span>
                    </TableCell>
                    <TableCell>
                      <RadioButtonGroup
                        id='confirmMalariaCasesClassifiedAsImported'
                        name='confirmMalariaCasesClassifiedAsImported'
                        color='primary'
                        options={[
                          new MultiSelectModel(
                            true,
                            t("indicators-responses:Common:Yes")
                          ),
                          new MultiSelectModel(
                            false,
                            t("indicators-responses:Common:No")
                          ),
                        ]}
                        value={summary.confirmMalariaCasesClassifiedAsImported}
                        defaultValue={bindConsistencyOverTimeForCoreIndicators(
                          summary?.confirmMalariaCasesClassifiedAsImported,
                          "confirmMalariaCasesClassifiedAsImported"
                        )}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                          onRadioValueChange(
                            "confirmMalariaCasesClassifiedAsImported",
                            e
                          )
                        }
                      />
                    </TableCell>
                  </>
                </TableRow>
                <TableRow>
                  <>
                    <TableCell>
                      <span className='px-3'>
                        8.&nbsp;
                        {t(
                          "Assessment.DataCollection.DataQualityAssessment.MalariaCasesDueToPF"
                        )}
                      </span>
                    </TableCell>
                    <TableCell>
                      <RadioButtonGroup
                        id='malariaCasesDueToPF'
                        name='malariaCasesDueToPF'
                        color='primary'
                        options={[
                          new MultiSelectModel(
                            true,
                            t("indicators-responses:Common:Yes")
                          ),
                          new MultiSelectModel(
                            false,
                            t("indicators-responses:Common:No")
                          ),
                        ]}
                        value={summary.malariaCasesDueToPF}
                        defaultValue={bindConsistencyOverTimeForCoreIndicators(
                          summary?.malariaCasesDueToPF,
                          "malariaCasesDueToPF"
                        )}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                          onRadioValueChange("malariaCasesDueToPF", e)
                        }
                      />
                    </TableCell>
                  </>
                </TableRow>
                <TableRow>
                  <>
                    <TableCell>
                      <span className='px-3'>
                        9.&nbsp;
                        {t(
                          "Assessment.DataCollection.DataQualityAssessment.MalariaCasesDueToPK"
                        )}
                      </span>
                    </TableCell>
                    <TableCell>
                      <RadioButtonGroup
                        id='malariaCasesDueToPK'
                        name='malariaCasesDueToPK'
                        color='primary'
                        options={[
                          new MultiSelectModel(
                            true,
                            t("indicators-responses:Common:Yes")
                          ),
                          new MultiSelectModel(
                            false,
                            t("indicators-responses:Common:No")
                          ),
                        ]}
                        value={summary.malariaCasesDueToPK}
                        defaultValue={bindConsistencyOverTimeForCoreIndicators(
                          summary?.malariaCasesDueToPK,
                          "malariaCasesDueToPK"
                        )}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                          onRadioValueChange("malariaCasesDueToPK", e)
                        }
                      />
                    </TableCell>
                  </>
                </TableRow>
                <TableRow>
                  <>
                    <TableCell>
                      <span className='px-3'>
                        10.&nbsp;
                        {t(
                          "Assessment.DataCollection.DataQualityAssessment.MalariaCasesDueToPM"
                        )}
                      </span>
                    </TableCell>
                    <TableCell>
                      <RadioButtonGroup
                        id='malariaCasesDueToPM'
                        name='malariaCasesDueToPM'
                        color='primary'
                        options={[
                          new MultiSelectModel(
                            true,
                            t("indicators-responses:Common:Yes")
                          ),
                          new MultiSelectModel(
                            false,
                            t("indicators-responses:Common:No")
                          ),
                        ]}
                        value={summary.malariaCasesDueToPM}
                        defaultValue={bindConsistencyOverTimeForCoreIndicators(
                          summary?.malariaCasesDueToPM,
                          "malariaCasesDueToPM"
                        )}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                          onRadioValueChange("malariaCasesDueToPM", e)
                        }
                      />
                    </TableCell>
                  </>
                </TableRow>
                <TableRow>
                  <>
                    <TableCell>
                      <span className='px-3'>
                        11.&nbsp;
                        {t(
                          "Assessment.DataCollection.DataQualityAssessment.MalariaCasesDueToPO"
                        )}
                      </span>
                    </TableCell>
                    <TableCell>
                      <RadioButtonGroup
                        id='malariaCasesDueToPO'
                        name='malariaCasesDueToPO'
                        color='primary'
                        options={[
                          new MultiSelectModel(
                            true,
                            t("indicators-responses:Common:Yes")
                          ),
                          new MultiSelectModel(
                            false,
                            t("indicators-responses:Common:No")
                          ),
                        ]}
                        value={summary.malariaCasesDueToPO}
                        defaultValue={bindConsistencyOverTimeForCoreIndicators(
                          summary?.malariaCasesDueToPO,
                          "malariaCasesDueToPO"
                        )}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                          onRadioValueChange("malariaCasesDueToPO", e)
                        }
                      />
                    </TableCell>
                  </>
                </TableRow>
                <TableRow>
                  <>
                    <TableCell>
                      <span className='px-3'>
                        12.&nbsp;
                        {t(
                          "Assessment.DataCollection.DataQualityAssessment.MalariaCasesDueToPV"
                        )}
                      </span>
                    </TableCell>
                    <TableCell>
                      <RadioButtonGroup
                        id='malariaCasesDueToPV'
                        name='malariaCasesDueToPV'
                        color='primary'
                        options={[
                          new MultiSelectModel(
                            true,
                            t("indicators-responses:Common:Yes")
                          ),
                          new MultiSelectModel(
                            false,
                            t("indicators-responses:Common:No")
                          ),
                        ]}
                        value={summary.malariaCasesDueToPV}
                        defaultValue={bindConsistencyOverTimeForCoreIndicators(
                          summary?.malariaCasesDueToPV,
                          "malariaCasesDueToPV"
                        )}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                          onRadioValueChange("malariaCasesDueToPV", e)
                        }
                      />
                    </TableCell>
                  </>
                </TableRow>
                <TableRow>
                  <>
                    <TableCell>
                      <span>
                        1.2.10 &nbsp;
                        {t(
                          "Assessment.DataCollection.DataQualityAssessment.KeyVariableConcordanceBtwTwoReportingSystem"
                        )}
                      </span>
                    </TableCell>
                    <TableCell>
                      <RadioButtonGroup
                        id='keyVariableConcordanceBtwTwoReportingSystem'
                        name='keyVariableConcordanceBtwTwoReportingSystem'
                        color='primary'
                        options={[
                          new MultiSelectModel(
                            true,
                            t("indicators-responses:Common:Met")
                          ),
                          new MultiSelectModel(
                            false,
                            t("indicators-responses:Common:NotMet")
                          ),
                        ]}
                        value={
                          summary.keyVariableConcordanceBtwTwoReportingSystem
                        }
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                          onRadioValueChange(
                            "keyVariableConcordanceBtwTwoReportingSystem",
                            e
                          )
                        }
                      />
                    </TableCell>
                  </>
                </TableRow>
                <TableRow>
                  <>
                    <TableCell>
                      <span>
                        1.2.11 &nbsp;
                        {t(
                          "Assessment.DataCollection.DataQualityAssessment.CoreVariableCompletenessWithinRegister"
                        )}
                      </span>
                    </TableCell>
                    <TableCell>
                      <TextBox
                        id='coreVariableCompletenessWithinRegister'
                        type='number'
                        name='coreVariableCompletenessWithinRegister'
                        maxLength={3}
                        fullWidth
                        inputProps={{
                          max: 100,
                          min: 0,
                        }}
                        className={`col-form-control inputfocus ${getCssClassForNationalLevelResult(summary?.coreVariableCompletenessWithinRegister)}`}
                        value={summary.coreVariableCompletenessWithinRegister}
                        onChange={(
                          evt: React.ChangeEvent<HTMLInputElement>
                        ) => {
                          onValueChange(
                            "coreVariableCompletenessWithinRegister",
                            evt
                          );
                        }}
                        error={
                          errors["coreVariableCompletenessWithinRegister"] &&
                          errors["coreVariableCompletenessWithinRegister"]
                        }
                        helperText={
                          errors["coreVariableCompletenessWithinRegister"] &&
                          errors["coreVariableCompletenessWithinRegister"]
                        }
                      />
                    </TableCell>
                  </>
                </TableRow>
                <TableRow>
                  <>
                    <TableCell>
                      <span>
                        1.2.12 &nbsp;
                        {t(
                          "Assessment.DataCollection.DataQualityAssessment.CoreVariableConcordanceBtwRegister"
                        )}
                      </span>
                    </TableCell>
                    <TableCell>
                      <TextBox
                        id='coreVariableConcordanceBtwRegister'
                        type='number'
                        name='coreVariableConcordanceBtwRegister'
                        maxLength={3}
                        fullWidth
                        inputProps={{
                          max: 100,
                          min: 0,
                        }}
                        className={`col-form-control inputfocus ${getCssClassForNationalLevelResult(summary?.coreVariableConcordanceBtwRegister)}`}
                        value={summary.coreVariableConcordanceBtwRegister}
                        onChange={(
                          evt: React.ChangeEvent<HTMLInputElement>
                        ) => {
                          onValueChange(
                            "coreVariableConcordanceBtwRegister",
                            evt
                          );
                        }}
                        error={
                          errors["coreVariableConcordanceBtwRegister"] &&
                          errors["coreVariableConcordanceBtwRegister"]
                        }
                        helperText={
                          errors["coreVariableConcordanceBtwRegister"] &&
                          errors["coreVariableConcordanceBtwRegister"]
                        }
                      />
                    </TableCell>
                  </>
                </TableRow>
              </>
            </TableBody>
          </>
        </Table>

        <div className='col-xs-12 col-md-6'>
          <div className='mt-3'>
            <p className='fw-bold'>
              <span>
                1.2.14 &nbsp;
                {t(
                  "Assessment.DataCollection.DataQualityAssessment.EliminationDesc1.2.14"
                )}
              </span>
            </p>
            <TextBox
              id='dataQualityResultReason'
              name='dataQualityResultReason'
              placeholder={t(
                "Assessment.DataCollection.DataQualityAssessment.EliminationPlaceholder1.2.14"
              )}
              rows={6}
              multiline
              fullWidth
              value={summary.dataQualityResultReason}
              onChange={(evt: React.ChangeEvent<HTMLInputElement>) => {
                dataQualityResultReason("dataQualityResultReason", evt);
              }}
              maxLength={2000}
              error={
                errors["dataQualityResultReason"] &&
                errors["dataQualityResultReason"]
              }
              helperText={
                errors["dataQualityResultReason"] &&
                errors["dataQualityResultReason"]
              }
            />
          </div>
        </div>

        <div className='button-action-section d-flex justify-content-center py-3'>
          {!summary?.isFinalized && canEditAndSaveDeskLevelDQASummaryResult && (
            <Button
              className={classNames("btn", "app-btn-secondary")}
              onClick={onSave}
            >
              {t("Common.Save")}
            </Button>
          )}
          {isDataSaved && canFinalizeDeskLevelDQASummaryResult && (
            <Button
              className={classNames("btn", "app-btn-secondary")}
              onClick={onFinalize}
            >
              {t("Common.Finalize")}
            </Button>
          )}
          {summary?.isFinalized && canExportDeskLevelDQASummary && (
            <Button
              className={classNames("btn", "app-btn-secondary")}
              onClick={onGenerateTemplate}
            >
              {t("Common.Export")}
            </Button>
          )}
        </div>

        <DownloadInProgressModal isFileDownloading={isFileDownloading} />
      </div>
    </>
  );
};

export default EliminationSummaryResult;
