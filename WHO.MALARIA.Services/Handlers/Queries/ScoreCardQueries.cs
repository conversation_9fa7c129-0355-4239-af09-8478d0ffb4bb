﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.DocumentManager;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Helper;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.SeedingMetadata;
using WHO.MALARIA.Features.Helpers;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Assessment;
using AutoMapper.Internal;

namespace WHO.MALARIA.Services.Handlers.Queries
{
    /// <summary>
    /// Provides queries to fetch data for score card
    /// </summary>
    public class ScoreCardQueries : RuleBase, IScoreCardQueries
    {
        private readonly IDbManager _dbManager;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ITranslationService _translationService;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;
        private readonly IScoreCard _scoreCardDocumentManager;
        private string _language => _httpContextAccessor?.HttpContext?.Request?.Cookies[Constants.Common.I18next] ?? Constants.Common.DefaultLanguage;
        public delegate string Translator(string key);

        public ScoreCardQueries(IDbManager dbManager, IHttpContextAccessor httpContextAccessor, IUnitOfWork unitOfWork, IScoreCard scoreCardDocumentManager, ITranslationService translationService, IAssessmentRuleChecker assessmentRuleChecker)
        {
            _dbManager = dbManager;
            _httpContextAccessor = httpContextAccessor;
            _unitOfWork = unitOfWork;
            _scoreCardDocumentManager = scoreCardDocumentManager;
            _translationService = translationService;
            _assessmentRuleChecker = assessmentRuleChecker;
        }

        #region Public Method 
        /// <summary>
        /// Get score card details for given assessment id and strategy id 
        /// </summary>
        /// <param name="assessmentId">Assessment id for which score card data are to be fetched</param>        
        /// <returns>Score card details for given assessment id and strategy id </returns>
        public async Task<ObjectivesSubObjectivesIndicatorsDetailsDto> GetScoreCardDetailsAsync(Guid assessmentId)
        {
            ScoreCardDto scoreCard = new ScoreCardDto();

            Guid strategyId = _unitOfWork.ScoreCardRepository.GetStrategyIdOfCaseSurveillanceType(assessmentId);

            IEnumerable<ScoreCardDetailsDto> deskReviewIndicatorsDetails = await _unitOfWork.ScoreCardRepository.GetDeskReviewIndicatorsAsync(assessmentId);

            IEnumerable<Domain.Dtos.IndicatorMetNotMetDto> deskReviewMetNotMetStatusDetails = await _unitOfWork.AssessmentDRResponseRepository.GetResponseStatusAsync(assessmentId);

            SetMetNotMetStatusToDeskReviewIndicators(deskReviewIndicatorsDetails, deskReviewMetNotMetStatusDetails, strategyId);

            if (StrategySeedingMetadata.BURDEN_REDUCTION_ID == strategyId || StrategySeedingMetadata.Both_ID == strategyId)
            {
                //Fetching percentage of desk level summary indicators from database and according to that set met not met status to particuler indicator

                DQADeskLevelSummaryDto dqaDeskLevelSummary = _unitOfWork.DQARepository.GetDeskLevelSummary(assessmentId);

                if (dqaDeskLevelSummary != null)
                {
                    //Using  SetMetNotMetStatus we are setting met not met status to indicator_1_2_1

                    ScoreCardDetailsDto indicator_1_2_1 = deskReviewIndicatorsDetails.FirstOrDefault(d => d.IndicatorSequence == "1.2.1");

                    SetMetNotMetStatus(indicator_1_2_1, dqaDeskLevelSummary.ReportCompleteness);

                    //Using  SetMetNotMetStatus we are setting met not met status to indicator_1_2_3

                    ScoreCardDetailsDto indicator_1_2_3 = deskReviewIndicatorsDetails.FirstOrDefault(d => d.IndicatorSequence == "1.2.3");
                    if (indicator_1_2_3 != null)
                    {
                        SetMetNotMetStatus(indicator_1_2_3, dqaDeskLevelSummary.ReportTimeliness);
                    }
                    
                    //Using  SetMetNotMetStatus we are setting met not met status to indicator_1_2_7

                    ScoreCardDetailsDto indicator_1_2_7 = deskReviewIndicatorsDetails.FirstOrDefault(d => d.IndicatorSequence == "1.2.7");

                    SetMetNotMetStatus(indicator_1_2_7, dqaDeskLevelSummary.VariableCompleteness);

                    //Using  SetMetNotMetStatus we are setting met not met status to indicator_1_2_8

                    ScoreCardDetailsDto indicator_1_2_8 = deskReviewIndicatorsDetails.FirstOrDefault(d => d.IndicatorSequence == "1.2.8");

                    SetMetNotMetStatus(indicator_1_2_8, dqaDeskLevelSummary.VariableConsistency);

                    //Using  SetMetNotMetStatus we are setting met not met status to indicator_1_2_10

                    ScoreCardDetailsDto indicator_1_2_10 = deskReviewIndicatorsDetails.FirstOrDefault(d => d.IndicatorSequence == "1.2.10");

                    SetMetNotMetStatus(indicator_1_2_10, dqaDeskLevelSummary.VariableConcordance);

                    //Using  SetMetNotMetStatus we are setting met not met status to indicator_1_2_9

                    ScoreCardDetailsDto indicator_1_2_9 = deskReviewIndicatorsDetails.FirstOrDefault(d => d.IndicatorSequence == "1.2.9");

                    //For setting met not met status to indicator 1_2_9 considering all indicators true/false values and adding into list
                    // Note: This is for ScoreCard display purposes - NA values are treated as false for percentage calculation

                    List<bool> consistencyOvertimeCoreIndicatorValues = new List<bool>();

                    consistencyOvertimeCoreIndicatorValues.Add(dqaDeskLevelSummary.MalariaOutpatientProportion ?? false);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaDeskLevelSummary.MalariaInPatientProportion ?? false);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaDeskLevelSummary.MalariaInPatientDeathProportion ?? false);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaDeskLevelSummary.TestPositivityRate ?? false);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaDeskLevelSummary.SlidePositivityRate ?? false);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaDeskLevelSummary.RDTPositivityRate ?? false);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaDeskLevelSummary.SuspectedTestProportion ?? false);

                    double consistencyTimeCoreIndicatorsStatus = Math.Round((consistencyOvertimeCoreIndicatorValues.Count(d => d == true) / 7.0) * 100);

                    SetMetNotMetStatus(indicator_1_2_9, (int)consistencyTimeCoreIndicatorsStatus);
                }
            }
            if (StrategySeedingMetadata.ELIMINATION_ID == strategyId || StrategySeedingMetadata.Both_ID == strategyId)
            {
                //Fetching percentage of elimination summary indicators from database and according to that set met not met status to particuler indicator

                DQAEliminationSummaryDto dqaEliminationSummary = _unitOfWork.DQARepository.GetEliminationSummary(assessmentId);

                if (dqaEliminationSummary != null)
                {
                    //Using  SetMetNotMetStatusForElimination we are setting met not met status to indicator_1_2_1

                    ScoreCardDetailsDto indicator_1_2_1 = deskReviewIndicatorsDetails.FirstOrDefault(d => d.IndicatorSequence == "1.2.1");

                    if (indicator_1_2_1 != null)
                    {
                        if (dqaEliminationSummary.ReportCompleteness)
                        {
                            indicator_1_2_1.IndicatorMetNotMetStatus = (int)Domain.Enum.MetNotMetStatus.Met;
                        }
                        else
                        {
                            indicator_1_2_1.IndicatorMetNotMetStatus = (int)Domain.Enum.MetNotMetStatus.NotMet;
                        }
                    }

                    //Using  SetMetNotMetStatusForElimination we are setting met not met status to indicator_1_2_2

                    ScoreCardDetailsDto indicator_1_2_2 = deskReviewIndicatorsDetails.FirstOrDefault(d => d.IndicatorSequence == "1.2.2");

                    SetMetNotMetStatusForElimination(indicator_1_2_2, dqaEliminationSummary.CaseInvestigationReportsCompleteness);


                    //Using  SetMetNotMetStatusForElimination we are setting met not met status to indicator_1_2_3

                    ScoreCardDetailsDto indicator_1_2_3 = deskReviewIndicatorsDetails.FirstOrDefault(d => d.IndicatorSequence == "1.2.3");


                    SetMetNotMetStatusForElimination(indicator_1_2_3, dqaEliminationSummary.ReportTimeliness);

                    //Using  SetMetNotMetStatusForElimination we are setting met not met status to indicator_1_2_4

                    ScoreCardDetailsDto indicator_1_2_4 = deskReviewIndicatorsDetails.FirstOrDefault(d => d.IndicatorSequence == "1.2.4");

                    SetMetNotMetStatusForElimination(indicator_1_2_4, dqaEliminationSummary.CaseNotificationReportsTimeliness);

                    //Using  SetMetNotMetStatusForElimination we are setting met not met status to indicator_1_2_5

                    ScoreCardDetailsDto indicator_1_2_5 = deskReviewIndicatorsDetails.FirstOrDefault(d => d.IndicatorSequence == "1.2.5");

                    SetMetNotMetStatusForElimination(indicator_1_2_5, dqaEliminationSummary.CaseInvestigationReportsTimeliness);

                    //Using  SetMetNotMetStatusForElimination we are setting met not met status to indicator_1_2_6

                    ScoreCardDetailsDto indicator_1_2_6 = deskReviewIndicatorsDetails.FirstOrDefault(d => d.IndicatorSequence == "1.2.6");

                    SetMetNotMetStatusForElimination(indicator_1_2_6, dqaEliminationSummary.FociInvestigationReportsTimeliness);

                    //Using  SetMetNotMetStatusForElimination we are setting met not met status to indicator_1_2_7

                    ScoreCardDetailsDto indicator_1_2_7 = deskReviewIndicatorsDetails.FirstOrDefault(d => d.IndicatorSequence == "1.2.7");

                    SetMetNotMetStatusForElimination(indicator_1_2_7, dqaEliminationSummary.CoreVariableCompletenessWithinReport);

                    //Using  SetMetNotMetStatusForElimination we are setting met not met status to indicator_1_2_8

                    ScoreCardDetailsDto indicator_1_2_8 = deskReviewIndicatorsDetails.FirstOrDefault(d => d.IndicatorSequence == "1.2.8");

                    SetMetNotMetStatusForElimination(indicator_1_2_8, dqaEliminationSummary.ConsistencyBetweenCoreVariables);

                    //Using  SetMetNotMetStatusForElimination we are setting met not met status to indicator_1_2_11

                    ScoreCardDetailsDto indicator_1_2_11 = deskReviewIndicatorsDetails.FirstOrDefault(d => d.IndicatorSequence == "1.2.11");

                    SetMetNotMetStatusForElimination(indicator_1_2_11, dqaEliminationSummary.CoreVariableCompletenessWithinRegister);

                    //Using  SetMetNotMetStatusForElimination we are setting met not met status to indicator_1_2_12

                    ScoreCardDetailsDto indicator_1_2_12 = deskReviewIndicatorsDetails.FirstOrDefault(d => d.IndicatorSequence == "1.2.12");

                    SetMetNotMetStatusForElimination(indicator_1_2_12, dqaEliminationSummary.CoreVariableConcordanceBtwRegister);


                    //Using  SetMetNotMetStatusForElimination we are setting met not met status to indicator_1_2_10

                    ScoreCardDetailsDto indicator_1_2_10 = deskReviewIndicatorsDetails.FirstOrDefault(d => d.IndicatorSequence == "1.2.10");

                    if (indicator_1_2_10 != null)
                    {
                        if (dqaEliminationSummary.KeyVariableConcordanceBtwTwoReportingSystem)
                        {
                            indicator_1_2_10.IndicatorMetNotMetStatus = (int)Domain.Enum.MetNotMetStatus.Met;
                        }
                        else
                        {
                            indicator_1_2_10.IndicatorMetNotMetStatus = (int)Domain.Enum.MetNotMetStatus.NotMet;
                        }
                    }

                    //Using  SetMetNotMetStatusForElimination we are setting met not met status to indicator_1_2_9

                    ScoreCardDetailsDto indicator_1_2_9 = deskReviewIndicatorsDetails.FirstOrDefault(d => d.IndicatorSequence == "1.2.9");

                    //For setting met not met status to indicator 1_2_9 considering all indicators true/false values and adding into list
                    // Note: This is for ScoreCard display purposes - NA values are treated as false for percentage calculation

                    List<bool> consistencyOvertimeCoreIndicatorValues = new List<bool>();

                    consistencyOvertimeCoreIndicatorValues.Add(dqaEliminationSummary.ConfirmMalariaCasesNotified ?? false);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaEliminationSummary.ConfirmMalariaCasesInvestigated ?? false);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaEliminationSummary.ConfirmMalariaCasesClassified ?? false);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaEliminationSummary.ConfirmMalariaCasesClassifiedAsLocal ?? false);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaEliminationSummary.ConfirmMalariaCasesClassifiedAsIndigenous ?? false);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaEliminationSummary.ConfirmMalariaCasesClassifiedAsIntroduced ?? false);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaEliminationSummary.ConfirmMalariaCasesClassifiedAsImported ?? false);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaEliminationSummary.MalariaCasesDueToPF ?? false);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaEliminationSummary.MalariaCasesDueToPK ?? false);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaEliminationSummary.MalariaCasesDueToPM ?? false);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaEliminationSummary.MalariaCasesDueToPO ?? false);
                    consistencyOvertimeCoreIndicatorValues.Add(dqaEliminationSummary.MalariaCasesDueToPV ?? false);

                    double consistencyTimeCoreIndicatorsStatus = Math.Round((consistencyOvertimeCoreIndicatorValues.Count(d => d == true) / 12.0) * 100);

                    SetMetNotMetStatusForElimination(indicator_1_2_9, (int)consistencyTimeCoreIndicatorsStatus);
                }
            }

            IEnumerable<DQAServiceLevelSummaryDto> servicLevelVariablesSummary = await _unitOfWork.ServiceLevelRepository.GetServiceLevelVariableDataAsync(assessmentId);

            //For dqa burden reduction strategy
            if (StrategySeedingMetadata.BURDEN_REDUCTION_ID == strategyId || StrategySeedingMetadata.Both_ID == strategyId)
            {
                if (servicLevelVariablesSummary.Any())
                {
                    ScoreCardDetailsDto indicator_1_2_11 = deskReviewIndicatorsDetails.FirstOrDefault(d => d.IndicatorSequence == "1.2.11");

                    int metNotMetStatus_1_2_11 = GetServiceLevelIndicatorMetNotMetStatus(indicator_1_2_11.IndicatorSequence, servicLevelVariablesSummary);

                    indicator_1_2_11.ServiceDeliveryMetNotMetStatus = (byte?)metNotMetStatus_1_2_11;

                    indicator_1_2_11.IsServiceDeliveryIndicator = true;

                    ScoreCardDetailsDto indicator_1_2_12 = deskReviewIndicatorsDetails.FirstOrDefault(d => d.IndicatorSequence == "1.2.12");

                    int metNotMetStatus_1_1_12 = GetServiceLevelIndicatorMetNotMetStatus(indicator_1_2_12.IndicatorSequence, servicLevelVariablesSummary);

                    indicator_1_2_12.ServiceDeliveryMetNotMetStatus = (byte?)metNotMetStatus_1_1_12;

                    indicator_1_2_12.IsServiceDeliveryIndicator = true;

                    ScoreCardDetailsDto indicator_1_2_13 = deskReviewIndicatorsDetails.FirstOrDefault(d => d.IndicatorSequence == "1.2.13");

                    int metNotMetStatus_1_1_13 = GetServiceLevelIndicatorMetNotMetStatus(indicator_1_2_13.IndicatorSequence, servicLevelVariablesSummary);

                    indicator_1_2_13.ServiceDeliveryMetNotMetStatus = (byte?)metNotMetStatus_1_1_13;

                    indicator_1_2_13.IsServiceDeliveryIndicator = true;

                }
            }
            //For dqa elimination strategy
            if (StrategySeedingMetadata.ELIMINATION_ID == strategyId || StrategySeedingMetadata.Both_ID == strategyId)
            {
                if (servicLevelVariablesSummary.Any())
                {
                    ScoreCardDetailsDto indicator_1_2_11 = deskReviewIndicatorsDetails.FirstOrDefault(d => d.IndicatorSequence == "1.2.11");

                    int metNotMetStatus_1_2_11 = GetServiceLevelIndicatorMetNotMetStatus(indicator_1_2_11.IndicatorSequence, servicLevelVariablesSummary);

                    indicator_1_2_11.ServiceDeliveryMetNotMetStatus = (byte?)metNotMetStatus_1_2_11;

                    indicator_1_2_11.IsServiceDeliveryIndicator = true;

                    ScoreCardDetailsDto indicator_1_2_12 = deskReviewIndicatorsDetails.FirstOrDefault(d => d.IndicatorSequence == "1.2.12");

                    int metNotMetStatus_1_1_12 = GetServiceLevelIndicatorMetNotMetStatus(indicator_1_2_12.IndicatorSequence, servicLevelVariablesSummary);

                    indicator_1_2_12.ServiceDeliveryMetNotMetStatus = (byte?)metNotMetStatus_1_1_12;

                    indicator_1_2_12.IsServiceDeliveryIndicator = true;
                }
            }

            Domain.Models.ScoreCard scoreCardDetails = await _unitOfWork.ScoreCardRepository.GetAsync(d => d.AssessmentId == assessmentId);
            IEnumerable<ScoreCardDetailsDto> scoreCardIndicatorsDetails = await _unitOfWork.ScoreCardRepository.GetSurveyIndicatorsAsync(assessmentId);

            scoreCardIndicatorsDetails.ToList().ForEach(indicator =>
            {
                ScoreCardDetailsDto indicatorDetail = deskReviewIndicatorsDetails.Where(i => i.IndicatorId == indicator.IndicatorId).FirstOrDefault();
                if (indicatorDetail == null)
                {
                    deskReviewIndicatorsDetails.Append(indicator);
                }
                else
                {
                    indicatorDetail.IsSurveyIndicator = indicator.IsSurveyIndicator;
                    indicatorDetail.SurveyMetNotMetStatus = indicator.SurveyMetNotMetStatus;
                    indicatorDetail.ReasonForResult = indicator.ReasonForResult;
                    indicatorDetail.Recommendation = indicator.Recommendation;
                }
            });

            scoreCard.ScoreCardDetails = deskReviewIndicatorsDetails;

            ObjectivesSubObjectivesIndicatorsDetailsDto objectiveSubObjectiveIndicators = new ObjectivesSubObjectivesIndicatorsDetailsDto();

            objectiveSubObjectiveIndicators.IsFinalized = scoreCardDetails != null ? scoreCardDetails.IsFinalized : false;

            objectiveSubObjectiveIndicators.IsFinalized = scoreCardDetails != null ? scoreCardDetails.IsFinalized : false;

            objectiveSubObjectiveIndicators.SubObjectives = deskReviewIndicatorsDetails.GroupBy(x => new
            {
                x.SubObjectiveId,
                x.SubObjectiveName,
                x.SubObjectiveSequence,
                x.ObjectiveId
            })
            .Select(x => new SubObjectiveDetailsDto
            {
                Id = x.Key.SubObjectiveId,
                Name = x.Key.SubObjectiveName,
                Sequence = x.Key.SubObjectiveSequence,
                ObjectiveId = x.Key.ObjectiveId,
                NationalScorePercentage = CalculateSubObjectiveNationalScorePercentage(x.Key.SubObjectiveId, deskReviewIndicatorsDetails),
                SurveyScorePercentage = CalculateSubObjectiveSurveyScorePercentage(x.Key.SubObjectiveId, deskReviewIndicatorsDetails),
                CountryLevelScorePercentage = CalculateCountryLevelScorePercentage(CalculateSubObjectiveNationalScorePercentage(x.Key.SubObjectiveId, deskReviewIndicatorsDetails),
                                                CalculateSubObjectiveSurveyScorePercentage(x.Key.SubObjectiveId, deskReviewIndicatorsDetails)),
                IsNotAssessed = IsNotAssessed(x.Key.SubObjectiveSequence, deskReviewIndicatorsDetails)
            }).ToList();

            objectiveSubObjectiveIndicators.Indicators = deskReviewIndicatorsDetails.GroupBy(x => new
            {
                x.IndicatorId,
                x.IndicatorName,
                x.IndicatorSequence,
                x.SubObjectiveId,
                x.IndicatorMetNotMetStatus,
                x.SurveyMetNotMetStatus,
                x.IsSurveyIndicator,
                x.IsServiceDeliveryIndicator,
                x.ServiceDeliveryMetNotMetStatus,
                x.ReasonForResult,
                x.Recommendation
            }).Select(x => new IndicatorDetailsDto
            {
                Id = x.Key.IndicatorId,
                Name = x.Key.IndicatorName,
                Sequence = x.Key.IndicatorSequence,
                SubObjectiveId = x.Key.SubObjectiveId,
                IndicatorMetNotMetStatus = x.Key.IndicatorMetNotMetStatus,
                SurveyMetNotMetStatus = x.Key.SurveyMetNotMetStatus,
                IsSurveyIndicator = x.Key.IsSurveyIndicator,
                IsServiceDeliveryIndicator = x.Key.IsServiceDeliveryIndicator,
                ServiceDeliveryMetNotMetStatus = x.Key.ServiceDeliveryMetNotMetStatus,
                ReasonForResult = x.Key.ReasonForResult,
                Recommendation = x.Key.Recommendation
            }).ToList();

            objectiveSubObjectiveIndicators.Objectives = deskReviewIndicatorsDetails.GroupBy(x => new
            {
                x.ObjectiveId,
                x.ObjectiveName,
                x.ObjectiveSequence
            }).Select(x => new ObjectiveDetailsDto
            {
                Id = x.Key.ObjectiveId,
                Name = x.Key.ObjectiveName,
                Sequence = x.Key.ObjectiveSequence,
                NationalScorePercentage = CalculateObjectiveNationalScorePercentages(x.Key.ObjectiveId, deskReviewIndicatorsDetails),
                SurveyScorePercentage = CalculateSurveyScorePercentage(x.Key.ObjectiveId, deskReviewIndicatorsDetails),
                IndicatorsMetPercentage = CalculateIndicatorsMetPercentage(x.Key.ObjectiveId, deskReviewIndicatorsDetails),
                TotalIndicators = CalculateTotalIndicatorsCount(x.Key.ObjectiveId, deskReviewIndicatorsDetails),
                TotalMetIndicators = CalculateTotalMetIndicatorsCount(x.Key.ObjectiveId, deskReviewIndicatorsDetails),
                CountryLevelScorePercentage = CalculateCountryLevelScorePercentage(CalculateNationalScorePercentage(x.Key.ObjectiveId, deskReviewIndicatorsDetails),
                                                CalculateSurveyScorePercentage(x.Key.ObjectiveId, deskReviewIndicatorsDetails))
            }).ToList();


            return objectiveSubObjectiveIndicators;
        }

        /// <summary>
        /// Get score card and file name
        /// </summary>
        /// <param name="assessmentId">The id of the assessment for which score card data is getting fetched</param>
        /// <returns>Instance of FileResponseDto</returns>
        public async Task<FileResponseDto> GetScoreCardExcelFileResponse(Guid assessmentId, Guid currentUserId)
        {
            CheckRule(new UserShouldHaveOperationPermissionOnAssessmentRule(_translationService, _assessmentRuleChecker, assessmentId, currentUserId, UserAssessmentPermission.CanExportScoreCard));

            FileResponseDto fileResponseDto = new FileResponseDto();

            ObjectivesSubObjectivesIndicatorsDetailsDto indicatorDetails = await GetScoreCardDetailsAsync(assessmentId);
            var indicatorsWithMetNotMetDetails = (from i in indicatorDetails.Indicators
                                                  join s in indicatorDetails.SubObjectives on i.SubObjectiveId equals s.Id
                                                  join o in indicatorDetails.Objectives on s.ObjectiveId equals o.Id
                                                  select new IndicatorsWithMetNotMetDetailsDto
                                                  {
                                                      ObjectiveName = o.Name,
                                                      SubObjectiveName = s.Name,
                                                      IndicatorSequence = i.Sequence,
                                                      IndicatorName = i.Name,
                                                      IndicatorMetNotMetStatus = i.IndicatorMetNotMetStatus,
                                                      MetNotMetStatus = i.SurveyMetNotMetStatus,
                                                      ServiceDeliveryMetNotMetStatus = i.ServiceDeliveryMetNotMetStatus,
                                                      ReasonForResult = i.ReasonForResult,
                                                      Recommendation = i.Recommendation
                                                  }).ToList();

            GetAssessmentOutputDto assessment = await _unitOfWork.AssessmentRepository.GetAssessment(assessmentId, currentUserId);
            var assessmentStartDate = $"{assessment.StartDate.Month}/{assessment.StartDate.Day}/{assessment.StartDate.Year}";
            var assessmentEndDate = $"{assessment.EndDate.Month}/{assessment.EndDate.Day}/{assessment.EndDate.Year}";

            Translator translator = new Translator(GetTranslation);

            DataTable dt = AnalyticalOutputHelper.GetDataTable(typeof(IndicatorsWithMetNotMetDetailsDto), indicatorsWithMetNotMetDetails, "ScoreCard", translator);

            Assessment assessmentDetails = await _unitOfWork.AssessmentRepository.GetAsync(d => d.Id == assessmentId);
            bool IsRapidAssessment = assessmentDetails.Approach == (int)AssessmentApproach.Rapid ? true : false;

            fileResponseDto.FileData = _scoreCardDocumentManager.GenerateExcel(dt, assessmentStartDate + " - " + assessmentEndDate, IsRapidAssessment);

            fileResponseDto.FileName = Constants.DownloadDocument.ScoreCardFileName;

            return fileResponseDto;
        }

        /// <summary>
        /// Checks whether the score card can be generated or not
        /// </summary>
        /// <param name="assessmentId">Assessment id for which score card data are to be fetched</param>        
        /// <returns>True, if score card can be generated else false</returns>
        public async Task<bool> CanScoreCardBeGeneratedAsync(Guid assessmentId)
        {
            Assessment assessmentDetails = await _unitOfWork.AssessmentRepository.GetAsync(d => d.Id == assessmentId);

            Guid strategyId = _unitOfWork.ScoreCardRepository.GetStrategyIdOfCaseSurveillanceType(assessmentId);

            bool hasDeskReviewIndicatorsCompleted = false;
            bool hasBurdenReductionFinalized = false;
            bool hasEliminationFinalized = false;
            bool hasBothFinalized = false;
            bool hasSurveyIndicatorsFinalized = false;
            bool canScoreCardBeGenerated = false;

            //Desk review priority indicators status
            IEnumerable<int> deskReviewIndicatorsStatuses = await _unitOfWork.AssessmentDRResponseRepository.GetResponseStatusesAsync(assessmentId, strategyId);
            var diagramsStatus = await _unitOfWork.AssessmentRepository.GetDiagramsStatusAsync(assessmentId, strategyId);

            if (deskReviewIndicatorsStatuses.Count() > 0 && deskReviewIndicatorsStatuses.All(d => d == (int)DeskReviewAssessmentResponseStatus.Completed))
            {
                hasDeskReviewIndicatorsCompleted = true;
            }

            //desk level dqa priority indicators status
            if (StrategySeedingMetadata.BURDEN_REDUCTION_ID == strategyId || StrategySeedingMetadata.Both_ID == strategyId)
            {
                bool hasDeskLevelSummaryFinalized = false;

                hasDeskLevelSummaryFinalized = _unitOfWork.DQARepository.GetIsFinalizedStatusOfDeskLevelSummaryAsync(assessmentId);

                //desk level dqa-service delivery priority indicators status
                bool hasDQAServiceDeliveryFinalized = _unitOfWork.DQARepository.GetIsFinalizedStatusOfServiceDeliveryAsync(assessmentId);

                if (hasDeskLevelSummaryFinalized)
                {
                    hasBurdenReductionFinalized = true;
                }

            }
            if (StrategySeedingMetadata.ELIMINATION_ID == strategyId || StrategySeedingMetadata.Both_ID == strategyId)
            {
                hasEliminationFinalized = _unitOfWork.DQARepository.GetIsFinalizedStatusOfEliminationSummaryAsync(assessmentId);
            }
            else if (StrategySeedingMetadata.Both_ID == strategyId)
            {
                bool hasDeskLevelSummaryFinalized = false;
                bool hasDeskLevelEliminationFinalized = false;
                bool hasserviceDeliveryFinalized = false;

                hasDeskLevelSummaryFinalized = _unitOfWork.DQARepository.GetIsFinalizedStatusOfDeskLevelSummaryAsync(assessmentId);

                hasDeskLevelEliminationFinalized = _unitOfWork.DQARepository.GetIsFinalizedStatusOfEliminationSummaryAsync(assessmentId);

                hasserviceDeliveryFinalized = _unitOfWork.DQARepository.GetIsFinalizedStatusOfEliminationSummaryAsync(assessmentId);

                if (hasDeskLevelSummaryFinalized && hasDeskLevelEliminationFinalized && hasserviceDeliveryFinalized)
                    hasBothFinalized = true;
            }

            //Question bank priority indicators status
            IEnumerable<bool> questionBankFinalizedFlagValues = await _unitOfWork.ShellTableRepository.GetIsFinalizedStatusOfQuestionBankAsync(assessmentId, strategyId);

            if (questionBankFinalizedFlagValues.All(d => d == true))
            {
                hasSurveyIndicatorsFinalized = true;
            }

            switch (assessmentDetails.Approach)
            {
                case (int)AssessmentApproach.Tailored:
                    {
                        if (hasDeskReviewIndicatorsCompleted == true && (hasBurdenReductionFinalized == true || hasEliminationFinalized == true || hasBothFinalized == true) && diagramsStatus.ObjectiveDiagramStatus == 2 && diagramsStatus.SubObjectiveDiagramStatus == 2)
                        {
                            canScoreCardBeGenerated = true;
                        }
                        break;
                    }
                case (int)AssessmentApproach.Comprehensive:
                    {
                        if (hasDeskReviewIndicatorsCompleted == true && (hasBurdenReductionFinalized == true || hasEliminationFinalized == true || hasBothFinalized == true) && hasSurveyIndicatorsFinalized == true && diagramsStatus.ObjectiveDiagramStatus == 2 && diagramsStatus.SubObjectiveDiagramStatus == 2)
                        {
                            canScoreCardBeGenerated = true;
                        }
                        break;
                    }
                case (int)AssessmentApproach.Rapid:
                    {
                        if (hasDeskReviewIndicatorsCompleted == true && (hasBurdenReductionFinalized == true || hasEliminationFinalized == true || hasBothFinalized == true) && diagramsStatus.ObjectiveDiagramStatus == 2 && diagramsStatus.SubObjectiveDiagramStatus == 2)
                        {
                            canScoreCardBeGenerated = true;
                        }
                        break;
                    }
            }
            return canScoreCardBeGenerated;
        }

        /// <summary>
        /// Checks whether the score card has data or not
        /// </summary>
        /// <param name="assessmentId">Assessment id for which score card data are to be fetched</param>        
        /// <returns>True, if score card data else false</returns>
        public async Task<bool> HasScoreCardDataAsync(Guid assessmentId)
        {
            if (assessmentId == Guid.Empty)
            {
                return false;
            }

            bool hasScroreCardData = _unitOfWork.ScoreCardRepository.HasScoreCardDataAsync(assessmentId);

            return hasScroreCardData;
        }
        #endregion

        #region Private methods
        /// <summary>
        /// Set met not met status to dqa indicator
        /// </summary>
        /// <param name="indicator">An object of ScoreCardDetailsDto</param>
        /// <param name="percentage">Indicator percentage</param>
        private void SetMetNotMetStatus(ScoreCardDetailsDto indicator, float percentage)
        {
            if (percentage > 95 && percentage <= 100)
            {
                indicator.IndicatorMetNotMetStatus = (int)Domain.Enum.MetNotMetStatus.Met;
            }
            else if (percentage <= 95 && percentage >= 80)
            {
                indicator.IndicatorMetNotMetStatus = (int)Domain.Enum.MetNotMetStatus.PartiallyMet;
            }
            else if ((percentage < 80 && percentage >= 0) || percentage > 100)
            {
                indicator.IndicatorMetNotMetStatus = (int)Domain.Enum.MetNotMetStatus.NotMet;
            }
            else
            {
                indicator.IndicatorMetNotMetStatus = (int)Domain.Enum.MetNotMetStatus.NotAssessed;
            }
        }

        /// <summary>
        /// Set met not met status to dqa indicator
        /// </summary>
        /// <param name="indicator">An object of ScoreCardDetailsDto</param>
        /// <param name="percentage">Indicator percentage</param>
        private void SetMetNotMetStatusForElimination(ScoreCardDetailsDto indicator, float percentage)
        {
            if (indicator != null)
            {
                if (percentage > 95 && percentage <= 100)
                {
                    indicator.IndicatorMetNotMetStatus = (int)Domain.Enum.MetNotMetStatus.Met;
                }
                else if (percentage <= 95 && percentage >= 80)
                {
                    indicator.IndicatorMetNotMetStatus = (int)Domain.Enum.MetNotMetStatus.PartiallyMet;
                }
                else if ((percentage < 80 && percentage >= 0) || percentage > 100)
                {
                    indicator.IndicatorMetNotMetStatus = (int)Domain.Enum.MetNotMetStatus.NotMet;
                }
                else
                {
                    indicator.IndicatorMetNotMetStatus = (int)Domain.Enum.MetNotMetStatus.NotAssessed;
                }
            }
        }

        /// <summary>
        /// Get national score percentage for objective indicators
        /// </summary>
        /// <param name="objectiveId">Objective Id for which national score percentage have to be calculated</param>
        /// <param name="indicatorsDetails">List of indicator details</param>
        /// <returns>National score percentage for objective indicators</returns>
        private int CalculateNationalScorePercentage(Guid objectiveId, IEnumerable<ScoreCardDetailsDto> indicatorsDetails)
        {
            int nationalScorePercentage = 0;

            int totalIndicatorsInSubObjectives = indicatorsDetails.Count(d => d.ObjectiveId == objectiveId && d.IndicatorSequence != "1.3.7" && d.IndicatorMetNotMetStatus != (int)Domain.Enum.MetNotMetStatus.NotAssessed && d.ServiceDeliveryMetNotMetStatus != (int)Domain.Enum.MetNotMetStatus.NotAssessed);

            if (indicatorsDetails.Where(d => d.IndicatorSequence == "1.2.11" || d.IndicatorSequence == "1.2.12").Any())
            {
                //indicator 1.2.11

                int? deskLevel_1_2_11_Indicator_MetNotMetStatus = indicatorsDetails.Where(d => d.ObjectiveId == objectiveId && d.IndicatorMetNotMetStatus != (int)Domain.Enum.MetNotMetStatus.NotAssessed && d.IndicatorSequence == "1.2.11").Select(d => d.IndicatorMetNotMetStatus).FirstOrDefault();

                int? serviceDelivery_1_2_11_Indicator_MetNotMetStatus = indicatorsDetails.Where(d => d.ObjectiveId == objectiveId && d.ServiceDeliveryMetNotMetStatus != (int)Domain.Enum.MetNotMetStatus.NotAssessed && d.IndicatorSequence == "1.2.11").Select(d => d.ServiceDeliveryMetNotMetStatus).FirstOrDefault();

                byte? avgOfIndicator_1_2_11 = Convert.ToByte((deskLevel_1_2_11_Indicator_MetNotMetStatus + serviceDelivery_1_2_11_Indicator_MetNotMetStatus) / 2);

                //indicator 1.2.12

                int? deskLevel_1_2_12_Indicator_MetNotMetStatus = indicatorsDetails.Where(d => d.ObjectiveId == objectiveId && d.IndicatorMetNotMetStatus != (int)Domain.Enum.MetNotMetStatus.NotAssessed && d.IndicatorSequence == "1.2.12").Select(d => d.IndicatorMetNotMetStatus).FirstOrDefault();

                int? serviceDelivery_1_2_12_Indicator_MetNotMetStatus = indicatorsDetails.Where(d => d.ObjectiveId == objectiveId && d.ServiceDeliveryMetNotMetStatus != (int)Domain.Enum.MetNotMetStatus.NotAssessed && d.IndicatorSequence == "1.2.12").Select(d => d.ServiceDeliveryMetNotMetStatus).FirstOrDefault();

                byte? avgOfIndicator_1_2_12 = Convert.ToByte((deskLevel_1_2_12_Indicator_MetNotMetStatus + serviceDelivery_1_2_12_Indicator_MetNotMetStatus) / 2);

            }

            int? sumOfMetNotMetStatusOfDeskLevelIndicators = indicatorsDetails.Where(d => d.ObjectiveId == objectiveId && d.IndicatorMetNotMetStatus != (int)Domain.Enum.MetNotMetStatus.NotAssessed && d.IndicatorSequence != "1.2.11" && d.IndicatorSequence != "1.2.12").Sum(s => s.IndicatorMetNotMetStatus);

            int? sumOfMetNotMetStatusOfServiceLevelIndicators = indicatorsDetails.Where(d => d.ObjectiveId == objectiveId && d.ServiceDeliveryMetNotMetStatus != (int)Domain.Enum.MetNotMetStatus.NotAssessed && d.IndicatorSequence != "1.2.11" && d.IndicatorSequence != "1.2.12").Sum(s => s.ServiceDeliveryMetNotMetStatus);

            if (totalIndicatorsInSubObjectives > 0)
                nationalScorePercentage = (int)Math.Round((((double)sumOfMetNotMetStatusOfDeskLevelIndicators + (double)sumOfMetNotMetStatusOfServiceLevelIndicators) / (double)(2 * totalIndicatorsInSubObjectives)) * 100);

            return nationalScorePercentage;
        }

        /// <summary>
        /// Get survey score percentage for objective indicators
        /// </summary>
        /// <param name="objectiveId">Objective Id for which survey score percentage have to be calculated</param>
        /// <param name="indicatorsDetails">List of indicator details</param>
        /// <returns>Survey score percentage for objective indicators</returns>
        private int CalculateSurveyScorePercentage(Guid objectiveId, IEnumerable<ScoreCardDetailsDto> indicatorsDetails)
        {
            int surveyScorePercentage = 0;

            int totalIndicatorsInObjective = indicatorsDetails.Count(d => d.ObjectiveId == objectiveId && d.IsSurveyIndicator == true && d.SurveyMetNotMetStatus != (int)Domain.Enum.MetNotMetStatus.NotAssessed);

            int? sumOfMetNotMetStatusOfAllIndicators = indicatorsDetails.Where(d => d.ObjectiveId == objectiveId && d.IsSurveyIndicator == true && d.SurveyMetNotMetStatus != (int)Domain.Enum.MetNotMetStatus.NotAssessed).Sum(s => s.SurveyMetNotMetStatus);

            if (totalIndicatorsInObjective > 0)
                surveyScorePercentage = (int)Math.Round(((double)sumOfMetNotMetStatusOfAllIndicators / (double)(2 * totalIndicatorsInObjective)) * 100);

            return surveyScorePercentage;
        }

        /// <summary>
        /// Gets the country score percentage for an objective indicators
        /// </summary>
        /// <param name="nationalScorePercentage">Percentage of national score</param>
        /// <param name="surveyScorePercentage">Percentage of survey score</param>
        /// <returns></returns>
        private int CalculateCountryLevelScorePercentage(int nationalScorePercentage, int surveyScorePercentage)
        {
            int countryLevelScorePercentage = (nationalScorePercentage + surveyScorePercentage) / 2;

            return countryLevelScorePercentage;
        }

        /// <summary>
        /// Get indicators met percentage for objective indicators
        /// </summary>
        /// <param name="objectiveId">Objective Id for which indicators met percentage have to be calculated</param>
        /// <param name="indicatorsDetails">List of indicator details</param>
        /// <returns>Indicators met percentage for objective indicators</returns>
        private int CalculateIndicatorsMetPercentage(Guid objectiveId, IEnumerable<ScoreCardDetailsDto> indicatorsDetails)
        {
            int indicatorsMetPercentage = 0;

            int totalIndicatorsInObjectives = indicatorsDetails.Count(d => d.ObjectiveId == objectiveId);

            int totalMetIndicatorsInObjective = indicatorsDetails.Count(d => d.ObjectiveId == objectiveId && (d.IndicatorMetNotMetStatus == (int)Domain.Enum.MetNotMetStatus.Met || d.ServiceDeliveryMetNotMetStatus == (int)Domain.Enum.MetNotMetStatus.Met));

            if (totalIndicatorsInObjectives > 0)
                indicatorsMetPercentage = (int)Math.Round(((double)totalMetIndicatorsInObjective / (double)(totalIndicatorsInObjectives)) * 100);

            return indicatorsMetPercentage;
        }

        /// <summary>
        /// Get number of indicators and total number of met indicators for objective indicators
        /// </summary>
        /// <param name="objectiveId">Objective Id for which number of indicators and total number of met indicators  have to be calculated</param>
        /// <param name="indicatorsDetails">List of indicator details</param>
        /// <returns>Number of indicators and total number of met indicators for objective indicators</returns>
        private int CalculateTotalIndicatorsCount(Guid objectiveId, IEnumerable<ScoreCardDetailsDto> indicatorsDetails)
        {
            int totalIndicatorsInObjective = indicatorsDetails.Count(d => d.ObjectiveId == objectiveId && d.IndicatorMetNotMetStatus != (int)Domain.Enum.MetNotMetStatus.NotAssessed && d.ServiceDeliveryMetNotMetStatus != (int)Domain.Enum.MetNotMetStatus.NotAssessed);

            return totalIndicatorsInObjective;
        }

        /// <summary>
        /// Get total met indicators percentage for objective indicators
        /// </summary>
        /// <param name="objectiveId">Objective Id for which total met indicators percentage have to be calculated</param>
        /// <param name="indicatorsDetails">List of indicator details</param>
        /// <returns>Total met indicators percentage indicators for objective indicators</returns>
        private int CalculateTotalMetIndicatorsCount(Guid objectiveId, IEnumerable<ScoreCardDetailsDto> indicatorsDetails)
        {
            int totalMetIndicatorsInObjective = indicatorsDetails.Count(d => d.ObjectiveId == objectiveId && (d.IndicatorMetNotMetStatus == (int)Domain.Enum.MetNotMetStatus.Met || d.ServiceDeliveryMetNotMetStatus == (int)Domain.Enum.MetNotMetStatus.Met));

            return totalMetIndicatorsInObjective;
        }

        private int CalculateObjectiveNationalScorePercentages(Guid objectiveId, IEnumerable<ScoreCardDetailsDto> indicatorsDetails)
        {
            int nationalScorePercentage = 0;

            //TODO: This commented code needs to revisit while implemeting new CR around score card 
            //List<Guid> subObejctivesIds = indicatorsDetails.Where(d => d.ObjectiveId == objectiveId).Select(d => d.SubObjectiveId).Distinct().ToList();

            //int total = 0;
            //int countOfSubobjectives = 0;

            //subObejctivesIds.ForEach(d =>
            //{
            //    int percentageOfSubObjective = CalculateSubObjectiveNationalScorePercentage(d, indicatorsDetails);

            //    total = total + percentageOfSubObjective;

            //    countOfSubobjectives = countOfSubobjectives + 1;
            //});

            //nationalScorePercentage = total / countOfSubobjectives;

            int? sumOfMetNotMetStatusOfDeskLevelIndicators = null;
            int? sumOfMetNotMetStatusOfServiceLevelIndicators = null;
            int totalIndicatorsInSubObjectives = 0;

            List<Guid> subObejctivesIds = indicatorsDetails.Where(d => d.ObjectiveId == objectiveId).Select(d => d.SubObjectiveId).Distinct().ToList();

            subObejctivesIds.ForEach(x =>
            {
                //Get total number of indicators count under the sub objectives
                int subObjectiveindicatorCount = indicatorsDetails.Count(d => d.SubObjectiveId == x && d.IndicatorSequence != "1.3.7" && ((d.IndicatorMetNotMetStatus != null && d.IndicatorMetNotMetStatus != (int)Domain.Enum.MetNotMetStatus.NotAssessed) || (d.ServiceDeliveryMetNotMetStatus != null && d.ServiceDeliveryMetNotMetStatus != (int)Domain.Enum.MetNotMetStatus.NotAssessed)));

                //Get total number of indicators count in each of the sub objectives
                totalIndicatorsInSubObjectives = totalIndicatorsInSubObjectives + subObjectiveindicatorCount;

                //Get sum of indicator met/partially met status of desk level in single sub objective
                int? sumOfMetNotMetStatusDeskLevel = indicatorsDetails.Where(d => d.SubObjectiveId == x && d.IndicatorMetNotMetStatus != (int)Domain.Enum.MetNotMetStatus.NotAssessed).Sum(s => s.IndicatorMetNotMetStatus);

                //Get total of indicator met/partially met status of desk level for all subobjective under one objective
                sumOfMetNotMetStatusOfDeskLevelIndicators = (sumOfMetNotMetStatusOfDeskLevelIndicators ?? 0) + sumOfMetNotMetStatusDeskLevel;

                //Get sum of indicator met/partially met status of service level in single sub objective
                int? sumOfMetNotMetStatusServiceLevel = indicatorsDetails.Where(d => d.SubObjectiveId == x && d.ServiceDeliveryMetNotMetStatus != (int)Domain.Enum.MetNotMetStatus.NotAssessed).Sum(s => s.ServiceDeliveryMetNotMetStatus);

                //Get total of indicator met/partially met status of service level for all subobjective under one objective
                sumOfMetNotMetStatusOfServiceLevelIndicators = (sumOfMetNotMetStatusOfServiceLevelIndicators ?? 0) + sumOfMetNotMetStatusServiceLevel;
            });


            //Calculate objective result national score percentage 
            // Sum of met/partially met status for each indicator for all subobjective under one objective and divide it
            // by total number of indicators * 2 from each sub objectives under one objective and
            // multiply by 100 to get exact figure.
            if (totalIndicatorsInSubObjectives > 0)
                nationalScorePercentage = (int)Math.Round((((double)sumOfMetNotMetStatusOfDeskLevelIndicators + (double)sumOfMetNotMetStatusOfServiceLevelIndicators) / (double)(2 * totalIndicatorsInSubObjectives)) * 100);

            return nationalScorePercentage;
        }

        /// <summary>
        /// Get national score percentage for sub objective indicators
        /// </summary>
        /// <param name="subObjectiveId">Sub objective Id for which national score percentage have to be calculated</param>
        /// <param name="indicatorsDetails">List of indicator details</param>
        /// <returns>National score percentage for sub objective indicators</returns>
        private int CalculateSubObjectiveNationalScorePercentage(Guid subObjectiveId, IEnumerable<ScoreCardDetailsDto> indicatorsDetails)
        {
            int nationalScorePercentage = 0;
            int avgOfIndicator_1_2_11 = 0;
            int avgOfIndicator_1_2_12 = 0;

            int totalIndicatorsInSubObjectives = indicatorsDetails.Count(d => d.SubObjectiveId == subObjectiveId && d.IndicatorSequence != "1.3.7" && ((d.IndicatorMetNotMetStatus != null && d.IndicatorMetNotMetStatus != (int)Domain.Enum.MetNotMetStatus.NotAssessed) || (d.ServiceDeliveryMetNotMetStatus != null && d.ServiceDeliveryMetNotMetStatus != (int)Domain.Enum.MetNotMetStatus.NotAssessed)));

            //TODO: This commented code needs to revisit while implemeting new CR around score card 
            //if (indicatorsDetails.Where(d => d.IndicatorSequence == "1.2.11" || d.IndicatorSequence == "1.2.12").Any())
            //{
            //    int? deskLevel_1_2_11_Indicator_MetNotMetStatus = indicatorsDetails.Where(d => d.SubObjectiveId == subObjectiveId && d.IndicatorMetNotMetStatus != (int)Domain.Enum.MetNotMetStatus.NotAssessed && d.IndicatorSequence == "1.2.11").Select(d => d.IndicatorMetNotMetStatus).FirstOrDefault();

            //    int? serviceDelivery_1_2_11_Indicator_MetNotMetStatus = indicatorsDetails.Where(d => d.SubObjectiveId == subObjectiveId && d.ServiceDeliveryMetNotMetStatus != (int)Domain.Enum.MetNotMetStatus.NotAssessed && d.IndicatorSequence == "1.2.11").Select(d => d.ServiceDeliveryMetNotMetStatus).FirstOrDefault();

            //    if (deskLevel_1_2_11_Indicator_MetNotMetStatus == null)
            //        deskLevel_1_2_11_Indicator_MetNotMetStatus = 0;
            //    if (serviceDelivery_1_2_11_Indicator_MetNotMetStatus == null)
            //        serviceDelivery_1_2_11_Indicator_MetNotMetStatus = 0;

            //    avgOfIndicator_1_2_11 = (int)Math.Round((((double)deskLevel_1_2_11_Indicator_MetNotMetStatus + (double)serviceDelivery_1_2_11_Indicator_MetNotMetStatus) / 2));

            //    int? deskLevel_1_2_12_Indicator_MetNotMetStatus = indicatorsDetails.Where(d => d.SubObjectiveId == subObjectiveId && d.IndicatorMetNotMetStatus != (int)Domain.Enum.MetNotMetStatus.NotAssessed && d.IndicatorSequence == "1.2.12").Select(d => d.IndicatorMetNotMetStatus).FirstOrDefault();

            //    int? serviceDelivery_1_2_12_Indicator_MetNotMetStatus = indicatorsDetails.Where(d => d.SubObjectiveId == subObjectiveId && d.ServiceDeliveryMetNotMetStatus != (int)Domain.Enum.MetNotMetStatus.NotAssessed && d.IndicatorSequence == "1.2.12").Select(d => d.ServiceDeliveryMetNotMetStatus).FirstOrDefault();

            //    if (deskLevel_1_2_12_Indicator_MetNotMetStatus == null)
            //        deskLevel_1_2_12_Indicator_MetNotMetStatus = 0;
            //    if (serviceDelivery_1_2_12_Indicator_MetNotMetStatus == null)
            //        serviceDelivery_1_2_12_Indicator_MetNotMetStatus = 0;

            //    avgOfIndicator_1_2_12 = (int)Math.Round((((double)deskLevel_1_2_12_Indicator_MetNotMetStatus + (double)serviceDelivery_1_2_12_Indicator_MetNotMetStatus) / 2));
            //}

            //int? sumOfMetNotMetStatusOfDeskLevelIndicators = indicatorsDetails.Where(d => d.SubObjectiveId == subObjectiveId && d.IndicatorMetNotMetStatus != (int)Domain.Enum.MetNotMetStatus.NotAssessed && d.IndicatorSequence != "1.2.11" && d.IndicatorSequence != "1.2.12").Sum(s => s.IndicatorMetNotMetStatus);

            //int? sumOfMetNotMetStatusOfServiceLevelIndicators = indicatorsDetails.Where(d => d.SubObjectiveId == subObjectiveId && d.ServiceDeliveryMetNotMetStatus != (int)Domain.Enum.MetNotMetStatus.NotAssessed && d.IndicatorSequence != "1.2.11" && d.IndicatorSequence != "1.2.12").Sum(s => s.ServiceDeliveryMetNotMetStatus);

            int? sumOfMetNotMetStatusOfDeskLevelIndicators = indicatorsDetails.Where(d => d.SubObjectiveId == subObjectiveId && d.IndicatorMetNotMetStatus != (int)Domain.Enum.MetNotMetStatus.NotAssessed).Sum(s => s.IndicatorMetNotMetStatus);

            int? sumOfMetNotMetStatusOfServiceLevelIndicators = indicatorsDetails.Where(d => d.SubObjectiveId == subObjectiveId && d.ServiceDeliveryMetNotMetStatus != (int)Domain.Enum.MetNotMetStatus.NotAssessed).Sum(s => s.ServiceDeliveryMetNotMetStatus);

            //if (totalIndicatorsInSubObjectives > 0)
            //nationalScorePercentage = (int)Math.Round((((double)sumOfMetNotMetStatusOfDeskLevelIndicators + (double)sumOfMetNotMetStatusOfServiceLevelIndicators + (double)avgOfIndicator_1_2_11 + (double)avgOfIndicator_1_2_12) / (double)(2 * totalIndicatorsInSubObjectives)) * 100);

            if (totalIndicatorsInSubObjectives > 0)
                nationalScorePercentage = (int)Math.Round((((double)sumOfMetNotMetStatusOfDeskLevelIndicators + (double)sumOfMetNotMetStatusOfServiceLevelIndicators) / (double)(2 * totalIndicatorsInSubObjectives)) * 100, MidpointRounding.AwayFromZero);

            return nationalScorePercentage;
        }

        /// <summary>
        /// Get survey score percentage for sub objective indicators
        /// </summary>
        /// <param name="objectiveId">Objective Id for which survey score percentage have to be calculated</param>
        /// <param name="indicatorsDetails">List of indicator details</param>
        /// <returns>Survey score percentage for sub objective indicators</returns>
        private int CalculateSubObjectiveSurveyScorePercentage(Guid subObjectiveId, IEnumerable<ScoreCardDetailsDto> indicatorsDetails)
        {
            int surveyScorePercentage = 0;

            int totalIndicatorsInObjective = indicatorsDetails.Count(d => d.SubObjectiveId == subObjectiveId && d.IsSurveyIndicator == true && d.SurveyMetNotMetStatus != (int)Domain.Enum.MetNotMetStatus.NotAssessed);

            int? sumOfMetNotMetStatusOfAllIndicators = indicatorsDetails.Where(d => d.SubObjectiveId == subObjectiveId && d.IsSurveyIndicator == true && d.SurveyMetNotMetStatus != (int)Domain.Enum.MetNotMetStatus.NotAssessed).Sum(s => s.SurveyMetNotMetStatus);

            if (totalIndicatorsInObjective > 0)
                surveyScorePercentage = (int)Math.Round(((double)sumOfMetNotMetStatusOfAllIndicators / (double)(2 * totalIndicatorsInObjective)) * 100);

            return surveyScorePercentage;
        }

        /// <summary>
        /// Set met not met status to desk review indicators
        /// </summary>
        /// <param name="deskReviewIndicatorsDetails">List of desk review indicator details</param>
        /// <param name="deskReviewMetNotMetStatusDetails">List of desk review indicators with met not met details</param>
        /// <param name="strategyId">StrategyId for which indicator data to be fetched</param>       
        private void SetMetNotMetStatusToDeskReviewIndicators(IEnumerable<ScoreCardDetailsDto> deskReviewIndicatorsDetails, IEnumerable<Domain.Dtos.IndicatorMetNotMetDto> deskReviewMetNotMetStatusDetails, Guid strategyId)
        {
            deskReviewIndicatorsDetails.ToList().ForEach(indicator =>
            {
                indicator.IndicatorMetNotMetStatus = deskReviewMetNotMetStatusDetails.FirstOrDefault(s => s.IndicatorId == indicator.IndicatorId && s.StrategyId == strategyId)?.MetNotMetStatus;
            });
        }

        /// <summary>
        /// Set IsNotAssessed flag based of indicator status
        /// </summary>
        /// <param name="subObjectiveSequence">Sub objective sequence</param>
        /// <param name="deskReviewIndicatorsDetails">Desk review indicators details</param>
        /// <returns>True or false flag based on indicator status</returns>
        private bool IsNotAssessed(string subObjectiveSequence, IEnumerable<ScoreCardDetailsDto> deskReviewIndicatorsDetails)
        {
            List<ScoreCardDetailsDto> scoreCardDetailsDtos = deskReviewIndicatorsDetails.Where(x => x.SubObjectiveSequence == subObjectiveSequence).ToList();

            return scoreCardDetailsDtos.TrueForAll(x => x.IndicatorMetNotMetStatus == (int)MetNotMetStatus.NotAssessed ? true : false);
        }

        /// <summary>
        /// Get dqa service level indicator met not met status
        /// </summary>
        /// <param name="indicatorSequence">Indicator sequence</param>
        /// <param name="dqaServiceLevelVariablesSummary">List of dqa service level variables summary</param>
        /// <returns>Met not met status</returns>
        private int GetServiceLevelIndicatorMetNotMetStatus(string indicatorSequence, IEnumerable<DQAServiceLevelSummaryDto> dqaServiceLevelVariablesSummary)
        {
            DQAServiceLevelSummaryDto dqaServicLevelSummary = new DQAServiceLevelSummaryDto();
            switch (indicatorSequence)
            {
                case "1.2.11":
                    dqaServicLevelSummary = dqaServiceLevelVariablesSummary.FirstOrDefault();
                    if (dqaServicLevelSummary != null && dqaServicLevelSummary.Total.HasValue)
                    {
                        return ((float)dqaServicLevelSummary.Total.Value * 100).GetDeskLevelBurdenReductionStrategyMetNotMetStatus();
                    }
                    else
                    {
                        return (int)Domain.Enum.MetNotMetStatus.NotAssessed;
                    }

                case "1.2.12":
                    dqaServicLevelSummary = dqaServiceLevelVariablesSummary.FirstOrDefault(d => d.DQAVariableId == DQAVariableSeedingMetadata.OverallConcordance_ID);
                    if (dqaServicLevelSummary != null && dqaServicLevelSummary.MonthConcordance.HasValue)
                    {
                        return ((float)dqaServicLevelSummary.MonthConcordance.Value * 100).GetDeskLevelBurdenReductionStrategyMetNotMetStatus();
                    }
                    else
                    {
                        return (int)Domain.Enum.MetNotMetStatus.NotAssessed;
                    }

                case "1.2.13":
                    IEnumerable<DQAServiceLevelSummaryDto> dqaSLErrorInDatasourcesVariables = dqaServiceLevelVariablesSummary.Where(d => d.DQAVariableId != DQAVariableSeedingMetadata.OverallConcordance_ID);
                    if (dqaSLErrorInDatasourcesVariables.Any())
                    {
                        int errorInDataSourceCountVariable = dqaSLErrorInDatasourcesVariables.Count(d => d.ErrorInDataSources.Value == 0);
                        if (errorInDataSourceCountVariable == dqaSLErrorInDatasourcesVariables.Count())
                        {
                            return (int)Domain.Enum.MetNotMetStatus.Met;
                        }
                        else
                        {
                            return (int)Domain.Enum.MetNotMetStatus.NotMet;
                        }
                    }
                    else
                    {
                        return (int)Domain.Enum.MetNotMetStatus.NotAssessed;
                    }

                default:
                    return (int)Domain.Enum.MetNotMetStatus.NotAssessed;
            }
        }

        /// <summary>
        /// Get translation for the given key 
        /// </summary>
        /// <param name="key">Key as a string</param>
        /// <returns>Translated text</returns>
        private string GetTranslation(string key)
        {
            if (string.IsNullOrEmpty(key))
                return key;

            return _translationService.GetTranslation(key, "translation");
        }
        #endregion
    }
}