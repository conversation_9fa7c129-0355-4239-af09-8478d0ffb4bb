using Microsoft.EntityFrameworkCore.Migrations;

namespace WHO.MALARIA.Database.Migrations
{
    public partial class MakeDeskLevelSummaryConsistentTrendFieldsNullable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<bool>(
                name: "SuspectedTestProportion",
                schema: "DQA",
                table: "DeskLevelSummary",
                type: "BIT",
                nullable: true,
                oldClrType: typeof(bool),
                oldType: "BIT");

            migrationBuilder.AlterColumn<bool>(
                name: "RDTPositivityRate",
                schema: "DQA",
                table: "DeskLevelSummary",
                type: "BIT",
                nullable: true,
                oldClrType: typeof(bool),
                oldType: "BIT");

            migrationBuilder.AlterColumn<bool>(
                name: "SlidePositivityRate",
                schema: "DQA",
                table: "DeskLevelSummary",
                type: "BIT",
                nullable: true,
                oldClrType: typeof(bool),
                oldType: "BIT");

            migrationBuilder.AlterColumn<bool>(
                name: "TestPositivityRate",
                schema: "DQA",
                table: "DeskLevelSummary",
                type: "BIT",
                nullable: true,
                oldClrType: typeof(bool),
                oldType: "BIT");

            migrationBuilder.AlterColumn<bool>(
                name: "MalariaInPatientDeathProportion",
                schema: "DQA",
                table: "DeskLevelSummary",
                type: "BIT",
                nullable: true,
                oldClrType: typeof(bool),
                oldType: "BIT");

            migrationBuilder.AlterColumn<bool>(
                name: "MalariaInPatientProportion",
                schema: "DQA",
                table: "DeskLevelSummary",
                type: "BIT",
                nullable: true,
                oldClrType: typeof(bool),
                oldType: "BIT");

            migrationBuilder.AlterColumn<bool>(
                name: "MalariaOutpatientProportion",
                schema: "DQA",
                table: "DeskLevelSummary",
                type: "BIT",
                nullable: true,
                oldClrType: typeof(bool),
                oldType: "BIT");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<bool>(
                name: "SuspectedTestProportion",
                schema: "DQA",
                table: "DeskLevelSummary",
                type: "BIT",
                nullable: false,
                oldClrType: typeof(bool),
                oldType: "BIT",
                oldNullable: true);

            migrationBuilder.AlterColumn<bool>(
                name: "RDTPositivityRate",
                schema: "DQA",
                table: "DeskLevelSummary",
                type: "BIT",
                nullable: false,
                oldClrType: typeof(bool),
                oldType: "BIT",
                oldNullable: true);

            migrationBuilder.AlterColumn<bool>(
                name: "SlidePositivityRate",
                schema: "DQA",
                table: "DeskLevelSummary",
                type: "BIT",
                nullable: false,
                oldClrType: typeof(bool),
                oldType: "BIT",
                oldNullable: true);

            migrationBuilder.AlterColumn<bool>(
                name: "TestPositivityRate",
                schema: "DQA",
                table: "DeskLevelSummary",
                type: "BIT",
                nullable: false,
                oldClrType: typeof(bool),
                oldType: "BIT",
                oldNullable: true);

            migrationBuilder.AlterColumn<bool>(
                name: "MalariaInPatientDeathProportion",
                schema: "DQA",
                table: "DeskLevelSummary",
                type: "BIT",
                nullable: false,
                oldClrType: typeof(bool),
                oldType: "BIT",
                oldNullable: true);

            migrationBuilder.AlterColumn<bool>(
                name: "MalariaInPatientProportion",
                schema: "DQA",
                table: "DeskLevelSummary",
                type: "BIT",
                nullable: false,
                oldClrType: typeof(bool),
                oldType: "BIT",
                oldNullable: true);

            migrationBuilder.AlterColumn<bool>(
                name: "MalariaOutpatientProportion",
                schema: "DQA",
                table: "DeskLevelSummary",
                type: "BIT",
                nullable: false,
                oldClrType: typeof(bool),
                oldType: "BIT",
                oldNullable: true);
        }
    }
}
