﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

using WHO.MALARIA.Domain.Models.DQA.DeskLevel;

namespace WHO.MALARIA.Database.EntityConfigurations
{
    public class DQADeskLevelSummaryConfiguration : IEntityTypeConfiguration<Summary>
    {
        /// <summary>
        /// Contains field level mappings for DQA desk level Summary domain model
        /// </summary>
        /// <param name="builder"></param>
        public void Configure(EntityTypeBuilder<Summary> builder)
        {
            builder.ToTable("DeskLevelSummary", MalariaSchemas.DQA);

            builder.HasKey(x => x.Id);

            builder.Property(x => x.AssessmentId).HasColumnName("AssessmentId").HasColumnType("UNIQUEIDENTIFIER").IsRequired();
            builder.Property(x => x.ReportCompleteness).HasColumnName("ReportCompleteness").HasColumnType("FLOAT");
            builder.Property(x => x.ReportTimeliness).HasColumnName("ReportTimeliness").HasColumnType("FLOAT");
            builder.Property(x => x.VariableCompleteness).HasColumnName("VariableCompleteness").HasColumnType("FLOAT");
            builder.Property(x => x.VariableConsistency).HasColumnName("VariableConsistency").HasColumnType("FLOAT");
            builder.Property(x => x.VariableConcordance).HasColumnName("VariableConcordance").HasColumnType("FLOAT");
            builder.Property(x => x.MalariaOutpatientProportion).HasColumnName("MalariaOutpatientProportion").HasColumnType("BIT");
            builder.Property(x => x.MalariaInPatientProportion).HasColumnName("MalariaInPatientProportion").HasColumnType("BIT");
            builder.Property(x => x.MalariaInPatientDeathProportion).HasColumnName("MalariaInPatientDeathProportion").HasColumnType("BIT");
            builder.Property(x => x.TestPositivityRate).HasColumnName("TestPositivityRate").HasColumnType("BIT");
            builder.Property(x => x.SlidePositivityRate).HasColumnName("SlidePositivityRate").HasColumnType("BIT");
            builder.Property(x => x.RDTPositivityRate).HasColumnName("RDTPositivityRate").HasColumnType("BIT");
            builder.Property(x => x.SuspectedTestProportion).HasColumnName("SuspectedTestProportion").HasColumnType("BIT");
            builder.Property(x => x.KeyVariableCompletenessWithinRegister).HasColumnName("KeyVariableCompletenessWithinRegister").HasColumnType("FLOAT");
            builder.Property(x => x.KeyVariableConcordanceBtwRegister).HasColumnName("KeyVariableConcordanceBtwRegister").HasColumnType("FLOAT");
            builder.Property(x => x.DataSoucesError).HasColumnName("DataSoucesError").HasColumnType("FLOAT");
            builder.Property(x => x.Type).HasColumnName("Type").HasColumnType("TINYINT").IsRequired();
            builder.Property(x => x.Year).HasColumnName("Year").HasColumnType("SMALLINT").IsRequired();
            builder.Property(b => b.IsFinalized).HasColumnName("IsFinalized").HasColumnType("BIT").IsRequired();

            builder.HasOne(x => x.Assessment).WithMany(x => x.Summaries).HasForeignKey(x => x.AssessmentId).OnDelete(DeleteBehavior.Cascade);
        }
    }
}
