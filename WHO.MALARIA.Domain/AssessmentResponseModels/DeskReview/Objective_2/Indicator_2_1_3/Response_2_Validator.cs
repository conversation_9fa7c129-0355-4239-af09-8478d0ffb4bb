﻿using FluentValidation;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_2.Indicator_2_1_3
{
    class Response_2_Validator : AbstractValidator<Response_2>
    {
        /// <summary>
        /// Contains validation rules for indicator 2.1.3
        /// </summary>
        public Response_2_Validator()
        {
            string stepBValidationMessage = "Please select 'Yes' or 'No' for all malaria control strategies in place and surveillance implemented";
            //Sets CascadeMode for all the rules within this validator
            CascadeMode = CascadeMode.StopOnFirstFailure;

            //CannotBeAssessedReason should be validated only if CannotBeAssessed check box is checked
            RuleFor(x => x.CannotBeAssessedReason)
            .NotEmpty().When(x => x.CannotBeAssessed == true);

            //Check for other rules only if 'CannotBeAssessed' is false
            When(x => x.CannotBeAssessed == false, () =>
                {
                    RuleFor(x => x).Must(k => k.Step_B.ChemoPreventionInPregnantWomen.SurveillanceImplemented != null).WithMessage(stepBValidationMessage);
                    RuleFor(x => x).Must(k => k.Step_B.ChemoPreventionInPregnantWomen.StrategyInPlace != null).WithMessage(stepBValidationMessage);
                    RuleFor(x => x).Must(k => k.Step_B.ChemoPreventionInInfancy.SurveillanceImplemented != null).WithMessage(stepBValidationMessage);
                    RuleFor(x => x).Must(k => k.Step_B.ChemoPreventionInInfancy.StrategyInPlace != null).WithMessage(stepBValidationMessage);
                    RuleFor(x => x).Must(k => k.Step_B.ChemoPreventionMDA.SurveillanceImplemented != null).WithMessage(stepBValidationMessage);
                    RuleFor(x => x).Must(k => k.Step_B.ChemoPreventionMDA.StrategyInPlace != null).WithMessage(stepBValidationMessage);
                    RuleFor(x => x).Must(k => k.Step_B.ChemoPreventionSMC.SurveillanceImplemented != null).WithMessage(stepBValidationMessage);
                    RuleFor(x => x).Must(k => k.Step_B.ChemoPreventionSMC.StrategyInPlace != null).WithMessage(stepBValidationMessage);
                    RuleFor(x => x).Must(k => k.Step_B.CommodityTracking.SurveillanceImplemented != null).WithMessage(stepBValidationMessage);
                    RuleFor(x => x).Must(k => k.Step_B.CommodityTracking.StrategyInPlace != null).WithMessage(stepBValidationMessage);
                    RuleFor(x => x).Must(k => k.Step_B.DrugEfficacy.SurveillanceImplemented != null).WithMessage(stepBValidationMessage);
                    RuleFor(x => x).Must(k => k.Step_B.DrugEfficacy.StrategyInPlace != null).WithMessage(stepBValidationMessage);
                    RuleFor(x => x).Must(k => k.Step_B.VectorControlRoutineChannel.SurveillanceImplemented != null).WithMessage(stepBValidationMessage);
                    RuleFor(x => x).Must(k => k.Step_B.VectorControlRoutineChannel.StrategyInPlace != null).WithMessage(stepBValidationMessage);
                    RuleFor(x => x).Must(k => k.Step_B.VectorControlMassCampaign.SurveillanceImplemented != null).WithMessage(stepBValidationMessage);
                    RuleFor(x => x).Must(k => k.Step_B.VectorControlMassCampaign.StrategyInPlace != null).WithMessage(stepBValidationMessage);
                    RuleFor(x => x).Must(k => k.Step_B.VectorControlIRS.SurveillanceImplemented != null).WithMessage(stepBValidationMessage);
                    RuleFor(x => x).Must(k => k.Step_B.VectorControlIRS.StrategyInPlace != null).WithMessage(stepBValidationMessage);
                    RuleFor(x => x).Must(k => k.Step_B.VectorControlLSM.SurveillanceImplemented != null).WithMessage(stepBValidationMessage);
                    RuleFor(x => x).Must(k => k.Step_B.VectorControlLSM.StrategyInPlace != null).WithMessage(stepBValidationMessage);
                    RuleFor(x => x).Must(k => k.Step_B.GenomicSurveillance.SurveillanceImplemented != null).WithMessage(stepBValidationMessage);
                    RuleFor(x => x).Must(k => k.Step_B.GenomicSurveillance.StrategyInPlace != null).WithMessage(stepBValidationMessage);
                    RuleFor(x => x).Must(k => k.Step_B.EntomologicalSurveillance.SurveillanceImplemented != null).WithMessage(stepBValidationMessage);
                    RuleFor(x => x).Must(k => k.Step_B.EntomologicalSurveillance.StrategyInPlace != null).WithMessage(stepBValidationMessage);
                });

        }
    }
}
