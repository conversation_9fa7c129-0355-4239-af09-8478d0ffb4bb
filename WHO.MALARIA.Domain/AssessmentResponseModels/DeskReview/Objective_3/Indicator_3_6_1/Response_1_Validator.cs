using FluentValidation;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_6_1
{
    /// <summary>
    /// Contains validation rules for indicator 3.6.1
    /// </summary>
    class Response_1_Validator : AbstractValidator<Response_1>
    {
        public Response_1_Validator()
        {
            //Sets CascadeMode for all the rules within this validator
            CascadeMode = CascadeMode.StopOnFirstFailure;

            //CannotBeAssessedReason should be validated only if CannotBeAssessed check box is checked
            RuleFor(x => x.CannotBeAssessedReason)
            .NotEmpty().When(x => x.CannotBeAssessed == true);

            //Check for other rules only if 'CannotBeAssessed' is false
            When(x => x.CannotBeAssessed == false, () =>
            {
                // National level validation - only required when CanAccessNational is true
                RuleForEach(x => x.AccessUsers)
                    .Must((response, accessUser) => !response.CanAccessNational || !string.IsNullOrWhiteSpace(accessUser.National))
                    .WithMessage("Please fill out all details to finalize the indicator");

                // SubNational level validation - only required when CanAccessSubNational is true
                RuleForEach(x => x.AccessUsers)
                    .Must((response, accessUser) => !response.CanAccessSubNational || !string.IsNullOrWhiteSpace(accessUser.SubNational))
                    .WithMessage("Please fill out all details for 'Subnational Level' to finalize the indicator");

                // Service Delivery level validation - only required when CanAccessServiceDelivery is true
                RuleForEach(x => x.AccessUsers)
                    .Must((response, accessUser) => !response.CanAccessServiceDelivery || !string.IsNullOrWhiteSpace(accessUser.ServiceDelivery))
                    .WithMessage("Please fill out all details for 'Service Delivery Level' to finalize the indicator");
            });
        }
    }
}
