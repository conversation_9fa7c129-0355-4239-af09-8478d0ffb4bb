﻿using System;

namespace WHO.MALARIA.Domain.Dtos
{
    /// <summary>
    /// Contains dqa desk level summary details 
    /// </summary>
    public class DQADeskLevelSummaryDto
    {
        public Guid AssessmentId { get; set; }
        public float ReportCompleteness { get; set; }
        public float ReportTimeliness { get; set; }
        public float VariableCompleteness { get; set; }
        public float VariableConsistency { get; set; }
        public float VariableConcordance { get; set; }
        public bool? MalariaOutpatientProportion { get; set; }
        public bool? MalariaInPatientProportion { get; set; }
        public bool? MalariaInPatientDeathProportion { get; set; }
        public bool? TestPositivityRate { get; set; }
        public bool? SlidePositivityRate { get; set; }
        public bool? RDTPositivityRate { get; set; }
        public bool? SuspectedTestProportion { get; set; }
    }
}
