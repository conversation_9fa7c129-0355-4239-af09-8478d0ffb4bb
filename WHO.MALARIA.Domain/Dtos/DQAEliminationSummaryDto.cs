﻿using System;

namespace WHO.MALARIA.Domain.Dtos
{
    /// <summary>
    /// Contains dqa elimination summary details 
    /// </summary>
    public class DQAEliminationSummaryDto
    {
        public Guid AssessmentId { get; set; }
        public bool ReportCompleteness { get; set; }
        public float ReportTimeliness { get; set; }
        public float CaseInvestigationReportsCompleteness { get; set; }
        public float CaseNotificationReportsTimeliness { get; set; }
        public float CaseInvestigationReportsTimeliness { get; set; }
        public float FociInvestigationReportsTimeliness { get; set; }
        public float CoreVariableCompletenessWithinReport { get; set; }
        public float ConsistencyBetweenCoreVariables { get; set; }
        public bool ConsistencyOverTimeCoreIndicators { get; set; }
        public float CoreVariableCompletenessWithinRegister { get; set; }
        public float CoreVariableConcordanceBtwRegister { get; set; }
        public bool? ConfirmMalariaCasesNotified { get; set; }
        public bool? ConfirmMalariaCasesInvestigated { get; set; }
        public bool? ConfirmMalariaCasesClassified { get; set; }
        public bool? ConfirmMalariaCasesClassifiedAsLocal { get; set; }
        public bool? ConfirmMalariaCasesClassifiedAsIndigenous { get; set; }
        public bool? ConfirmMalariaCasesClassifiedAsIntroduced { get; set; }
        public bool? ConfirmMalariaCasesClassifiedAsImported { get; set; }
        public bool? MalariaCasesDueToPF { get; set; }
        public bool? MalariaCasesDueToPK { get; set; }
        public bool? MalariaCasesDueToPM { get; set; }
        public bool? MalariaCasesDueToPO { get; set; }
        public bool? MalariaCasesDueToPV { get; set; }
        public bool KeyVariableConcordanceBtwTwoReportingSystem { get; set; }
    }
}
