﻿using System.Collections.Generic;

namespace WHO.MALARIA.Domain.Dtos.OutputDtos.DeskLevelDQA
{
    /// <summary>
    /// Contains national level indicator summary front end response
    /// </summary>
    public class IndicatorNationalLevelSummaryDataDto
    {
        //TODO: check with <PERSON><PERSON><PERSON> about response format and modify this accordingly
        public Dictionary<string, object> DataQualityChecksWithData { get; set; }
        //public Dictionary<string, Dictionary<string, bool?>> ConsistencyOverTimeChecks { get; set; }
        public short Year { get; set; }
        public string DataQualityResultReason { get; set; }
    }

    /// <summary>
    /// Contains national level indicator summary response
    /// </summary>
    public class DeskLevelNationalLevelSummaryDto
    {
        public double? ReportingCompleteness { get; set; }
        public double? ReportingTimeliness { get; set; }
        public double? ReportingVariableCompleteness { get; set; }
        public double? ReportingConsistencyBtwVariables { get; set; }
        public double? ReportingConcordance { get; set; }
        public ConsistencyOverTimeCheck ReportingConsistencyOverTime { get; set; }
        public byte Type { get; set; }
    }

    /// <summary>
    /// Contains national level indicator consistency over time check response
    /// </summary>
    public class ConsistencyOverTimeCheck
    {
        public bool? ProportionOfMalariaOutpatients { get; set; }
        public bool? ProportionOfMalariaInpatients { get; set; }
        public bool? ProportionOfMalariaInpatientDeaths { get; set; }
        public bool? TestPositivityRate { get; set; }
        public bool? SlidePositivityRate { get; set; }
        public bool? RDTPositivityRate { get; set; }
        public bool? ProportionOfSuspectsTested { get; set; }
    }
}
