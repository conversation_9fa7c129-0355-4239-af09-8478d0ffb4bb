﻿using System;

namespace WHO.MALARIA.Domain.Dtos.InputDtos.DeskLevelDQA
{
    /// <summary>
    /// Model to handle input parameters required for saving national level summary result data
    /// </summary>
    public class NationalLevelSummaryRequestDto
    {
        public Guid AssessmentId { get; set; }
        public double? ReportCompleteness { get; set; }
        public double? ReportTimeliness { get; set; }
        public double? VariableCompleteness { get; set; }
        public double? VariableConsistency { get; set; }
        public double? VariableConcordance { get; set; }
        public bool? MalariaOutpatientProportion { get; set; }
        public bool? MalariaInPatientProportion { get; set; }
        public bool? MalariaInPatientDeathProportion { get; set; }
        public bool? TestPositivityRate { get; set; }
        public bool? SlidePositivityRate { get; set; }
        public bool? RDTPositivityRate { get; set; }
        public bool? SuspectedTestProportion { get; set; }
        public double? KeyVariableCompletenessWithinRegister { get; set; }
        public double? KeyVariableConcordanceBtwRegister { get; set; }
        public double? DataSoucesError { get; set; }
        public byte Type { get; set; } //1 For NationalLevelResult 2 for NationalLevelTarget
        public short Year { get; set; }
        public string DataQualityResultReason { get; set; }
        public bool IsFinalized { get; set; }
    }
}
